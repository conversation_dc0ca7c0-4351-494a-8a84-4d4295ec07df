#!/usr/bin/env python3
"""
阶段二完整功能测试脚本
验证前端规则配置界面和集成功能
"""
import requests
import json
import time

def test_backend_rules_api():
    """测试后端规则API"""
    print("🔧 测试后端规则API...")
    
    try:
        # 测试规则列表
        response = requests.get("http://localhost:8002/api/v1/rules", timeout=10)
        print(f"  规则列表API: {response.status_code}")
        if response.status_code == 200:
            rules = response.json()
            print(f"  - 可用规则数: {len(rules)}")
            for rule in rules[:3]:
                print(f"    • {rule['name']}: {rule['description'][:50]}...")
        
        # 测试规则模板
        response = requests.get("http://localhost:8002/api/v1/rules/templates", timeout=10)
        print(f"  规则模板API: {response.status_code}")
        if response.status_code == 200:
            templates = response.json()
            print(f"  - 可用模板数: {len(templates)}")
        
        return True
    except Exception as e:
        print(f"  ❌ 后端规则API测试失败: {e}")
        return False

def test_rule_execution():
    """测试规则执行功能"""
    print("\n⚙️ 测试规则执行功能...")
    
    test_text = """M：主任您好，华为委托的第三方。
1：好，您说。
M：想了解一下我们跟华为合作的使用它的产品方案。
1：嗯，得有一二十年了，基本上用了它网络安全什么服务器什么一类的。"""
    
    try:
        # 测试规则执行
        response = requests.post(
            "http://localhost:8002/api/v1/rules/execute",
            json={"text": test_text},
            timeout=15
        )
        
        print(f"  规则执行API: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"  - 转换成功: {result.get('success', False)}")
            print(f"  - 应用规则数: {len(result.get('rules_applied', []))}")
            print(f"  - 执行时间: {result.get('total_execution_time_ms', 0):.2f}ms")
            print(f"  - 原文长度: {len(result.get('original_text', ''))}")
            print(f"  - 转换后长度: {len(result.get('transformed_text', ''))}")
        
        return True
    except Exception as e:
        print(f"  ❌ 规则执行测试失败: {e}")
        return False

def test_mixed_processing():
    """测试混合处理功能"""
    print("\n🔄 测试混合处理功能...")
    
    test_text = """M：您好，请问您对华为的产品有什么看法？
1：我觉得华为的产品还是不错的，技术比较先进。
M：那您觉得有什么需要改进的地方吗？
1：嗯，我觉得价格可以再便宜一些。"""
    
    modes = [
        ("rules_only", "仅规则引擎"),
        ("hybrid", "混合处理"),
        ("rules_then_llm", "先规则后LLM")
    ]
    
    success_count = 0
    
    for mode, description in modes:
        try:
            response = requests.post(
                "http://localhost:8002/api/v1/transform",
                json={
                    "text": test_text,
                    "processing_mode": mode,
                    "llm_provider": "mock"
                },
                timeout=20
            )
            
            print(f"  {description} ({mode}): {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"    - 状态: {result.get('status')}")
                print(f"    - 处理模式: {result.get('processing_mode')}")
                
                processing_info = result.get('processing_info', {})
                if processing_info:
                    print(f"    - 执行时间: {processing_info.get('execution_time_ms', 0):.2f}ms")
                    print(f"    - 规则应用: {processing_info.get('rules_applied', 0)}")
                    print(f"    - LLM使用: {'是' if processing_info.get('llm_used', False) else '否'}")
                
                success_count += 1
            
        except Exception as e:
            print(f"    ❌ {description}测试失败: {e}")
    
    return success_count == len(modes)

def test_frontend_accessibility():
    """测试前端页面可访问性"""
    print("\n🌐 测试前端页面可访问性...")
    
    pages = [
        ("http://localhost:3001", "首页"),
        ("http://localhost:3001/dashboard", "转换页面"),
        ("http://localhost:3001/rules", "规则管理页面")
    ]
    
    success_count = 0
    
    for url, name in pages:
        try:
            response = requests.get(url, timeout=10)
            print(f"  {name}: {response.status_code}")
            if response.status_code == 200:
                success_count += 1
                # 检查页面内容
                content = response.text
                if "规则" in content or "转换" in content or "笔录" in content:
                    print(f"    ✅ 页面内容正常")
                else:
                    print(f"    ⚠️ 页面内容可能异常")
        except Exception as e:
            print(f"    ❌ {name}访问失败: {e}")
    
    return success_count == len(pages)

def test_file_upload():
    """测试文件上传功能"""
    print("\n📁 测试文件上传功能...")
    
    try:
        # 创建测试文件
        test_content = """M：主任您好，华为委托的第三方。
1：好，您说。
M：想了解一下我们跟华为合作的使用它的产品方案。
1：得有一二十年了，基本上用了它网络安全什么服务器什么一类的。"""
        
        files = {'file': ('test.txt', test_content, 'text/plain')}
        
        response = requests.post(
            "http://localhost:8002/api/v1/file/upload",
            files=files,
            timeout=15
        )
        
        print(f"  文件上传API: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"  - 文件ID: {result.get('file_id')}")
            print(f"  - 文件名: {result.get('original_filename')}")
            print(f"  - 文件大小: {result.get('file_size')} bytes")
            print(f"  - 字符数: {result.get('character_count')}")
            return True
        else:
            print(f"  ❌ 上传失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 文件上传测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 阶段二完整功能测试开始...")
    print("=" * 60)
    
    tests = [
        ("后端规则API", test_backend_rules_api),
        ("规则执行功能", test_rule_execution),
        ("混合处理功能", test_mixed_processing),
        ("前端页面访问", test_frontend_accessibility),
        ("文件上传功能", test_file_upload),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 阶段二功能测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 阶段二所有功能测试通过！")
        print("\n✅ 阶段二交付标准验证:")
        print("  ✅ 支持多种格式文件上传和处理")
        print("  ✅ 规则配置功能可用 (前端界面已实现)")
        print("  ✅ 转换质量达到基本要求")
        print("  ✅ 完整的转换工作流程")
        print("\n🚀 阶段二开发完成，可以进入阶段三！")
    else:
        print("⚠️ 部分功能测试失败，需要进一步检查")
    
    print("\n📍 可用的功能地址:")
    print("  - 转换页面: http://localhost:3001/dashboard")
    print("  - 规则管理: http://localhost:3001/rules")
    print("  - API文档: http://localhost:8002/docs")

if __name__ == "__main__":
    main()
