#!/usr/bin/env python3
"""
混合处理引擎测试脚本
"""
import requests
import json

def test_hybrid_processing():
    """测试混合处理引擎"""
    test_text = """M：主任您好，华为委托的第三方。
1：好，您说。
M：想了解一下我们跟华为合作的使用它的产品方案。
1：嗯，得有一二十年了，基本上用了它网络安全什么服务器什么一类的。
M：那您觉得华为的产品质量怎么样？
1：啊，我觉得还是不错的，技术比较先进。"""

    # 测试不同的处理模式
    modes = [
        ("rules_only", "仅规则引擎"),
        ("llm_only", "仅LLM"),
        ("hybrid", "混合处理"),
        ("rules_then_llm", "先规则后LLM"),
        ("llm_then_rules", "先LLM后规则")
    ]
    
    print("🧪 混合处理引擎测试")
    print("=" * 60)
    
    for mode, description in modes:
        print(f"\n🔍 测试模式: {description} ({mode})")
        print("-" * 40)
        
        try:
            data = {
                "text": test_text,
                "processing_mode": mode,
                "llm_provider": "mock"
            }
            
            response = requests.post(
                "http://localhost:8002/api/v1/transform",
                json=data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ 状态: {result.get('status')}")
                print(f"📊 处理模式: {result.get('processing_mode')}")
                
                # 显示处理信息
                processing_info = result.get('processing_info', {})
                if processing_info:
                    print(f"⏱️  执行时间: {processing_info.get('execution_time_ms', 0):.2f}ms")
                    print(f"📏 规则应用数: {processing_info.get('rules_applied', 0)}")
                    print(f"🤖 LLM使用: {'是' if processing_info.get('llm_used', False) else '否'}")
                    
                    # 显示质量指标
                    quality_metrics = processing_info.get('quality_metrics', {})
                    if quality_metrics:
                        print(f"📈 字符保留率: {quality_metrics.get('char_retention_rate', 0):.2%}")
                        print(f"📄 行保留率: {quality_metrics.get('line_retention_rate', 0):.2%}")
                        print(f"🎯 内容相似度: {quality_metrics.get('content_similarity', 0):.2%}")
                
                # 显示转换结果预览
                transformed = result.get('transformed_text', '')
                preview = transformed[:150] + "..." if len(transformed) > 150 else transformed
                print(f"📝 转换结果预览:\n{preview}")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 混合处理引擎测试完成！")

if __name__ == "__main__":
    test_hybrid_processing()
