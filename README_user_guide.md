# 用户流程详细指南

## 👤 详细用户流程与界面设计

### 用户界面布局架构

#### 1. 首页 (Landing Page)
- **路由**: `/`
- **功能**: 项目宣传、功能介绍、登录/注册入口
- **设计要点**: 
  - 清晰的价值主张展示
  - 功能演示和案例展示
  - 简洁的登录/注册入口

#### 2. 主应用界面 (Dashboard)
- **路由**: `/dashboard`
- **布局**: 左侧历史记录侧边栏 + 右侧主要内容区域
- **功能**: 用户登录后的主要工作区域
- **设计要点**:
  - 可收起的历史记录侧边栏
  - 默认展示笔录上传功能
  - 响应式设计支持移动端

#### 3. 规则配置与管理界面
- **路由**: `/rules`
- **布局**: 顶部规则集选择 + 左侧规则导航 + 右侧详情区域
- **功能**: 规则集选择、规则展示、编辑、配置、测试
- **设计要点**:
  - 三级结构化规则展示
  - 实时预览和测试功能
  - 定制规则生成入口

#### 4. 转换结果对比界面
- **路由**: `/result`
- **布局**: 顶部指标卡片 + 主要左右对比区域
- **功能**: 转换结果展示、差异对比、下载
- **设计要点**:
  - 可视化差异标记
  - 同步滚动支持
  - 检验指标实时计算

#### 5. 定制专属规则浮窗
- **触发**: 从规则配置界面点击"定制专属规则"按钮
- **类型**: 模态框组件
- **功能**: 训练数据上传、文件配对确认、规则生成
- **设计要点**:
  - 拖拽上传支持
  - 智能文件配对展示
  - 进度指示和状态反馈

### 详细用户操作流程

#### 步骤0: 用户认证与历史记录访问

**登录/注册流程**:
1. **访问首页**: 用户访问应用根路由
2. **身份验证检查**: 系统检查用户登录状态
3. **登录/注册**: 未登录用户通过模态框进行身份验证
4. **状态管理**: 成功登录后更新全局认证状态
5. **重定向**: 自动跳转到主应用界面

**历史记录访问**:
- **记录列表**: 左侧侧边栏展示用户历史转换记录
- **记录信息**: 显示原始笔录摘要、转换时间、使用规则集
- **快速操作**: 支持重新查看、下载、删除等操作
- **搜索过滤**: 支持按时间、规则集、关键词过滤
- **分页加载**: 大量历史记录的分页展示

#### 步骤1: 笔录上传与预处理

**文本直接输入**:
```
用户操作流程:
1. 点击文本输入区域
2. 粘贴或手动输入笔录内容
3. 系统实时统计字符数和行数
4. 自动调整文本框高度
5. 实时预览格式保持效果
```

**文件上传功能**:
```
用户操作流程:
1. 点击上传区域或拖拽文件
2. 系统验证文件格式(.txt/.docx)
3. 文件大小和内容安全检查
4. 解析文件内容并展示预览
5. 保持原始文件换行格式
```

**支持的详细功能**:
- **实时统计**: 字符数、词数、段落数统计
- **格式预览**: 严格保持原始文件格式
- **内容验证**: 检查是否包含对话标记
- **错误处理**: 文件格式错误、大小超限等提示
- **自动保存**: 临时保存用户输入内容

**技术实现要点**:
- 使用FileReader API读取文件内容
- 支持拖拽上传的HTML5特性
- 文件类型MIME检查和扩展名验证
- 大文件的分块读取和进度显示

#### 步骤2: 规则配置与管理详细流程

**规则集选择与切换**:
```
用户操作流程:
1. 界面顶部显示当前规则集
2. 点击下拉菜单查看所有可用规则集
3. 选择通用规则集或个人定制规则集
4. 系统加载对应规则配置
5. 右侧区域更新规则详情展示
```

**规则三级结构导航**:
```
层级结构:
规则分类 (一级)
├── 语言处理
│   ├── 语气调整 (二级规则条目)
│   │   ├── 去除口语词 (三级子规则)
│   │   └── 语调统一
│   └── 视角转换
│       ├── 第一人称转换
│       └── 去除访谈者发言
└── 内容整理
    ├── 段落合并
    └── 主题分类
```

**规则详情区域功能**:
- **规则解释**: 清晰说明每条规则的作用和效果
- **处理方式配置**: 选择LLM处理或确定性规则处理
- **案例展示**: 显示该规则的转换前后对比案例
- **参数调整**: 可调整的规则参数和阈值设置
- **开关控制**: 快速启用/禁用特定规则

**定制专属规则生成流程**:
```
详细操作步骤:
1. 点击"定制专属规则"按钮
2. 弹出训练数据上传浮窗
3. 上传配对的原始-转换后笔录文件
4. 系统智能识别和配对文件
5. 用户确认配对关系
6. 提交数据开始规则生成
7. 实时显示生成进度
8. 生成完成后自动加载新规则集
```

**规则即时测试功能**:
- **测试文本输入**: 用户输入或AI生成测试文本
- **规则应用**: 使用当前配置的规则进行转换
- **结果预览**: 实时显示转换结果
- **效果对比**: 开关特定规则查看效果差异
- **参数调优**: 根据测试结果调整规则参数

#### 步骤3: 笔录转换与结果分析

**转换过程管理**:
```
转换流程:
1. 用户点击"开始转换"按钮
2. 系统验证笔录内容和规则配置
3. 创建转换任务并分配处理器
4. 实时显示处理进度和状态
5. 分阶段处理：预处理→规则应用→后处理
6. 完成后自动跳转到结果对比界面
```

**结果对比界面功能**:
- **左右对比视图**: 原始笔录与转换后笔录并排显示
- **差异高亮显示**: 
  - 🟢 新增内容
  - 🔴 删除内容  
  - 🟡 修改内容
  - ⚪ 未变化内容
- **同步滚动**: 左右两侧内容同步滚动定位
- **段落对应**: 显示原始段落与转换后段落的对应关系

**检验指标详细计算**:

**定量指标卡片**:
- **字数保留率**: `(转换后字数 / 原始字数) × 100%`
  - 🟢 60-80%: 合格范围
  - 🟡 50-60% 或 80-90%: 注意范围
  - 🔴 <50% 或 >90%: 警告范围

- **原文完全引用率**: `(完全相同片段字数 / 原始字数) × 100%`
  - 🟢 >50%: 合格
  - 🟡 30-50%: 注意
  - 🔴 <30%: 警告

- **原文实质保持率**: `(实质一致片段字数 / 原始字数) × 100%`
  - 🟢 >75%: 合格
  - 🟡 60-75%: 注意  
  - 🔴 <60%: 警告

**定性指标评估**:
- **视角一致性**: 检查是否全程使用第一人称叙述
- **主题分类**: 评估内容是否按主题合理分段
- **语义连贯性**: 分析句子和段落间的逻辑连接

**指标自定义功能**:
- **阈值调整**: 用户可自定义各指标的合格标准
- **权重设置**: 为不同指标设置重要性权重
- **自定义指标**: 添加项目特定的评估指标
- **指标保存**: 保存个人指标配置供后续使用

#### 步骤4: 结果导出与保存

**下载功能详细选项**:
- **文件格式**:
  - `.txt`: 纯文本格式，保持简单格式
  - `.docx`: Word文档格式，保持富文本格式
  - `.pdf`: PDF格式，用于正式文档
  - `.json`: 包含转换配置和元数据的结构化格式

- **命名规则**:
  - 自动命名：`转换后笔录_YYYYMMDD_HHMMSS`
  - 自定义命名：用户输入文件名
  - 项目命名：基于项目或主题的命名模板

- **内容选择**:
  - 仅转换结果
  - 包含原始对比
  - 包含转换说明
  - 包含检验指标

### 功能扩展规划

#### 短期扩展 (1-2个月)
- [ ] **批量处理功能**: 
  - 支持同时上传多个笔录文件
  - 批量应用相同规则配置
  - 批量导出处理结果

- [ ] **转换历史记录增强**:
  - 历史记录标签分类
  - 收藏和星标功能
  - 历史记录搜索和过滤

#### 中期扩展 (3-6个月)
- [ ] **协作功能**:
  - 团队规则集共享
  - 协作标注和审核
  - 版本控制和变更追踪

- [ ] **高级分析功能**:
  - 转换质量趋势分析
  - 规则效果统计
  - 个人使用习惯分析

#### 长期扩展 (6个月以上)
- [ ] **多语言支持**: 
  - 支持英文、日文等其他语言笔录
  - 跨语言转换功能
  - 多语言界面国际化

- [ ] **AI助手功能**:
  - 智能推荐最优规则配置
  - 自动质量问题诊断
  - 转换效果预测

---

更多信息请参考：
- [技术架构](README_architecture.md)
- [开发指南](README_development.md)
- [部署指南](README_deployment.md) 