#!/usr/bin/env python3
"""
API测试脚本
"""
import requests
import json

def test_health():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:8002/api/v1/health")
        print(f"健康检查: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_transform():
    """测试文本转换"""
    try:
        data = {
            "text": "M：您好\n1：好的",
            "rules": [],
            "llm_provider": "mock"
        }
        response = requests.post(
            "http://localhost:8002/api/v1/transform",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        print(f"文本转换: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"转换结果: {result.get('transformed_text', '')[:100]}...")
        else:
            print(f"错误: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"文本转换失败: {e}")
        return False

def test_file_upload():
    """测试文件上传"""
    try:
        # 创建测试文件
        test_content = "M：主任您好\n1：好，您说。"
        with open("test_upload.txt", "w", encoding="utf-8") as f:
            f.write(test_content)
        
        # 上传文件
        with open("test_upload.txt", "rb") as f:
            files = {"file": ("test_upload.txt", f, "text/plain")}
            response = requests.post("http://localhost:8002/api/v1/file/upload", files=files)
        
        print(f"文件上传: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"文件ID: {result.get('file_id')}")
            print(f"文件大小: {result.get('file_size')} bytes")
            return True, result.get('file_id')
        else:
            print(f"错误: {response.text}")
            return False, None
    except Exception as e:
        print(f"文件上传失败: {e}")
        return False, None

if __name__ == "__main__":
    print("🧪 开始API测试...")
    print()
    
    # 测试健康检查
    print("1. 测试健康检查")
    health_ok = test_health()
    print()
    
    # 测试文本转换
    print("2. 测试文本转换")
    transform_ok = test_transform()
    print()
    
    # 测试文件上传
    print("3. 测试文件上传")
    upload_ok, file_id = test_file_upload()
    print()
    
    # 总结
    print("📊 测试结果:")
    print(f"  健康检查: {'✅' if health_ok else '❌'}")
    print(f"  文本转换: {'✅' if transform_ok else '❌'}")
    print(f"  文件上传: {'✅' if upload_ok else '❌'}")
    
    if all([health_ok, transform_ok, upload_ok]):
        print("\n🎉 所有API测试通过！")
    else:
        print("\n⚠️  部分API测试失败，请检查后端服务")
