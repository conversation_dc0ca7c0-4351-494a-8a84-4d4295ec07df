#!/usr/bin/env python3
"""
启动后端服务器的脚本
"""
import uvicorn
from app.main import app

if __name__ == "__main__":
    print("🚀 启动笔录转换系统后端服务...")
    print("📍 服务地址: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    print("❤️  健康检查: http://localhost:8001/api/v1/health")
    print()
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
