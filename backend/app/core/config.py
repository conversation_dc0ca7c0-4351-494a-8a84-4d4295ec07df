"""
应用配置管理
"""
from typing import List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用设置"""
    
    # 应用基础配置
    APP_NAME: str = Field(default="笔录转换系统", description="应用名称")
    APP_VERSION: str = Field(default="0.1.0", description="应用版本")
    DEBUG: bool = Field(default=True, description="调试模式")
    SECRET_KEY: str = Field(default="dev-secret-key", description="密钥")
    
    # 数据库配置
    DATABASE_URL: str = Field(default="sqlite:///./app.db", description="数据库连接URL")
    
    # LLM 配置
    LLM_PROVIDER: str = Field(default="deepseek", description="LLM提供商")
    DEEPSEEK_API_KEY: str = Field(default="", description="Deepseek API密钥")
    DEEPSEEK_BASE_URL: str = Field(default="https://api.deepseek.com", description="Deepseek API地址")
    OPENAI_API_KEY: str = Field(default="", description="OpenAI API密钥")
    OLLAMA_BASE_URL: str = Field(default="http://localhost:11434", description="Ollama服务地址")
    OLLAMA_MODEL: str = Field(default="qwen2.5:7b-instruct", description="Ollama模型名称")
    
    # 文件上传配置
    MAX_FILE_SIZE: int = Field(default=10485760, description="最大文件大小(字节)")
    UPLOAD_PATH: str = Field(default="./uploads", description="文件上传路径")
    ALLOWED_EXTENSIONS: str = Field(default="txt,docx", description="允许的文件扩展名")
    
    # CORS 配置
    CORS_ORIGINS: str = Field(
        default="http://localhost:3000,http://127.0.0.1:3000",
        description="允许的跨域源(逗号分隔)"
    )

    @property
    def cors_origins_list(self) -> List[str]:
        """将CORS_ORIGINS字符串转换为列表"""
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()
