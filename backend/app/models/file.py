"""
文件相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.sql import func
from app.core.database import Base


class FileRecord(Base):
    """文件记录模型"""
    __tablename__ = "file_records"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 文件基础信息
    file_id = Column(String(36), unique=True, index=True, nullable=False, comment="文件唯一ID")
    original_filename = Column(String(255), nullable=False, comment="原始文件名")
    stored_filename = Column(String(255), nullable=False, comment="存储文件名")
    file_size = Column(Integer, nullable=False, comment="文件大小(字节)")
    file_type = Column(String(10), nullable=False, comment="文件类型")
    
    # 文件内容信息
    text_content = Column(Text, nullable=True, comment="提取的文本内容")
    character_count = Column(Integer, nullable=True, comment="字符数")
    line_count = Column(Integer, nullable=True, comment="行数")
    
    # 文件状态
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    is_deleted = Column(Boolean, default=False, comment="是否已删除")
    
    # 存储路径
    upload_path = Column(String(500), nullable=False, comment="上传路径")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<FileRecord(id={self.id}, filename={self.original_filename})>"


class DownloadRecord(Base):
    """下载记录模型"""
    __tablename__ = "download_records"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 下载信息
    download_id = Column(String(36), unique=True, index=True, nullable=False, comment="下载唯一ID")
    filename = Column(String(255), nullable=False, comment="下载文件名")
    file_format = Column(String(10), nullable=False, comment="文件格式")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    
    # 关联信息
    source_transform_id = Column(Integer, nullable=True, comment="关联的转换记录ID")
    
    # 下载状态
    download_count = Column(Integer, default=0, comment="下载次数")
    is_expired = Column(Boolean, default=False, comment="是否已过期")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    last_downloaded_at = Column(DateTime(timezone=True), nullable=True, comment="最后下载时间")
    
    def __repr__(self):
        return f"<DownloadRecord(id={self.id}, filename={self.filename})>"
