"""
规则引擎模块
提供规则定义、执行和管理功能
"""

from .models import (
    Rule, RuleCondition, RuleAction, RuleSet, 
    RuleExecutionResult, TransformationResult,
    RuleTemplate, RuleValidationResult,
    RuleType, OperatorType, ActionType
)

from .engine import RuleEngine
from .presets import RulePresets

__all__ = [
    # 数据模型
    'Rule', 'RuleCondition', 'RuleAction', 'RuleSet',
    'RuleExecutionResult', 'TransformationResult',
    'RuleTemplate', 'RuleValidationResult',
    
    # 枚举类型
    'RuleType', 'OperatorType', 'ActionType',
    
    # 核心类
    'RuleEngine', 'RulePresets'
]
