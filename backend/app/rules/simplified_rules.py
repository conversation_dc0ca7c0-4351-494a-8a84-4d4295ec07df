"""
简化的规则系统
基于训练数据设计的层级化规则结构
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel


class RuleExample(BaseModel):
    """规则转换示例"""
    input_text: str
    output_text: str
    description: str


class SubRule(BaseModel):
    """二级规则（具体规则）"""
    id: str
    name: str
    description: str
    enabled: bool = True
    implementation_type: str  # "deterministic" 或 "llm"
    examples: List[RuleExample] = []
    test_cases: List[str] = []  # 测试用例


class MainRule(BaseModel):
    """一级规则"""
    id: str
    name: str
    description: str
    enabled: bool = True
    sub_rules: List[SubRule] = []


class RuleCategory(BaseModel):
    """规则大类"""
    id: str
    name: str
    description: str
    icon: str
    enabled: bool = True
    main_rules: List[MainRule] = []


class RuleSet(BaseModel):
    """规则集"""
    id: str
    name: str
    description: str
    is_default: bool = False
    categories: List[RuleCategory] = []


class SimplifiedRulePresets:
    """简化的规则预设"""
    
    @staticmethod
    def get_default_ruleset() -> RuleSet:
        """获取默认规则集"""
        return RuleSet(
            id="default_ruleset",
            name="默认规则集",
            description="基于训练数据的标准转换规则",
            is_default=True,
            categories=[
                SimplifiedRulePresets._get_content_filtering_category(),
                SimplifiedRulePresets._get_format_optimization_category(),
                SimplifiedRulePresets._get_content_organization_category(),
                SimplifiedRulePresets._get_language_optimization_category(),
            ]
        )
    
    @staticmethod
    def _get_content_filtering_category() -> RuleCategory:
        """内容过滤大类"""
        return RuleCategory(
            id="content_filtering",
            name="内容过滤",
            description="过滤和筛选访谈内容",
            icon="🔍",
            main_rules=[
                MainRule(
                    id="speaker_filtering",
                    name="发言人过滤",
                    description="过滤访谈者和主持人的发言",
                    sub_rules=[
                        SubRule(
                            id="moderator_filter",
                            name="访谈者过滤",
                            description="过滤以'M：'开头的访谈者发言",
                            implementation_type="deterministic",
                            examples=[
                                RuleExample(
                                    input_text="M：主任您好，华为委托的第三方。",
                                    output_text="[已过滤]",
                                    description="过滤访谈者的问题"
                                )
                            ],
                            test_cases=[
                                "M：主任您好，华为委托的第三方。",
                                "M：想了解一下我们跟华为合作的使用它的产品方案。"
                            ]
                        ),
                        SubRule(
                            id="question_filter",
                            name="问题引导过滤",
                            description="过滤顾问的引导性问题",
                            implementation_type="deterministic",
                            examples=[
                                RuleExample(
                                    input_text="顾问：您与华为的合作情况是怎样的？",
                                    output_text="[已过滤]",
                                    description="过滤顾问问题"
                                )
                            ],
                            test_cases=[
                                "顾问：您与华为的合作情况是怎样的？",
                                "顾问：您对华为有何期望和建议？"
                            ]
                        )
                    ]
                ),
                MainRule(
                    id="noise_filtering",
                    name="噪音过滤",
                    description="过滤无意义的语气词和填充词",
                    sub_rules=[
                        SubRule(
                            id="filler_words",
                            name="语气词过滤",
                            description="去除'嗯'、'啊'、'那个'等语气词",
                            implementation_type="deterministic",
                            examples=[
                                RuleExample(
                                    input_text="嗯，得有一二十年了。",
                                    output_text="得有一二十年了。",
                                    description="去除语气词"
                                )
                            ],
                            test_cases=[
                                "嗯，得有一二十年了。",
                                "啊，我觉得还是不错的。"
                            ]
                        ),
                        SubRule(
                            id="repetition_filter",
                            name="重复内容过滤",
                            description="去除重复的表述",
                            implementation_type="llm",
                            examples=[
                                RuleExample(
                                    input_text="基本上用了它网络安全什么服务器什么一类的，基本上全用了。",
                                    output_text="用了它网络安全、服务器等产品，基本上全用了。",
                                    description="合并重复表述"
                                )
                            ]
                        )
                    ]
                )
            ]
        )
    
    @staticmethod
    def _get_format_optimization_category() -> RuleCategory:
        """格式优化大类"""
        return RuleCategory(
            id="format_optimization",
            name="格式优化",
            description="优化文本格式和结构",
            icon="📝",
            main_rules=[
                MainRule(
                    id="speaker_removal",
                    name="发言人标识处理",
                    description="处理发言人标识符",
                    sub_rules=[
                        SubRule(
                            id="speaker_prefix_removal",
                            name="发言人前缀去除",
                            description="去除'1：'等发言人标识",
                            implementation_type="deterministic",
                            examples=[
                                RuleExample(
                                    input_text="1：好，您说。",
                                    output_text="好，您说。",
                                    description="去除发言人标识"
                                )
                            ],
                            test_cases=[
                                "1：好，您说。",
                                "1：得有一二十年了。"
                            ]
                        )
                    ]
                ),
                MainRule(
                    id="punctuation_optimization",
                    name="标点符号优化",
                    description="规范标点符号使用",
                    sub_rules=[
                        SubRule(
                            id="punctuation_standardization",
                            name="标点符号规范化",
                            description="统一标点符号格式",
                            implementation_type="deterministic",
                            examples=[
                                RuleExample(
                                    input_text="网络安全什么服务器什么一类的",
                                    output_text="网络安全、服务器等产品",
                                    description="规范标点符号"
                                )
                            ]
                        )
                    ]
                )
            ]
        )
    
    @staticmethod
    def _get_content_organization_category() -> RuleCategory:
        """内容组织大类"""
        return RuleCategory(
            id="content_organization",
            name="内容组织",
            description="按主题组织和分类内容",
            icon="📋",
            main_rules=[
                MainRule(
                    id="topic_classification",
                    name="主题分类",
                    description="将内容按主题进行分类",
                    sub_rules=[
                        SubRule(
                            id="product_topic",
                            name="产品相关内容",
                            description="识别和分类产品相关的讨论",
                            implementation_type="llm",
                            examples=[
                                RuleExample(
                                    input_text="基本上用了它网络安全什么服务器什么一类的，基本上全用了，这几大品类全用了。",
                                    output_text="【产品】基本上用了网络安全、服务器等产品，这几大品类全用了。",
                                    description="标记产品相关内容"
                                )
                            ]
                        ),
                        SubRule(
                            id="cooperation_topic",
                            name="合作情况内容",
                            description="识别合作情况相关内容",
                            implementation_type="llm",
                            examples=[
                                RuleExample(
                                    input_text="得有一二十年了。",
                                    output_text="【整体合作情况】我们和华为合作得有二十年的时间。",
                                    description="标记合作情况"
                                )
                            ]
                        ),
                        SubRule(
                            id="feedback_topic",
                            name="反馈建议内容",
                            description="识别用户反馈和建议",
                            implementation_type="llm",
                            examples=[
                                RuleExample(
                                    input_text="价格相较其他要贵不少，我们采购的话确实是有点不太好中标。",
                                    output_text="【未来期望和建议】价格相较其他要贵不少，希望在价格方面能够更有竞争力。",
                                    description="标记反馈建议"
                                )
                            ]
                        )
                    ]
                )
            ]
        )
    
    @staticmethod
    def _get_language_optimization_category() -> RuleCategory:
        """语言优化大类"""
        return RuleCategory(
            id="language_optimization",
            name="语言优化",
            description="优化语言表达和流畅度",
            icon="✨",
            main_rules=[
                MainRule(
                    id="expression_improvement",
                    name="表达优化",
                    description="改善语言表达的流畅度",
                    sub_rules=[
                        SubRule(
                            id="colloquial_to_formal",
                            name="口语转书面语",
                            description="将口语化表达转为正式表达",
                            implementation_type="llm",
                            examples=[
                                RuleExample(
                                    input_text="你像飞腾有，但是鲲鹏的量是最大的。",
                                    output_text="像飞腾等产品也有，但是鲲鹏的市场占有量是最大的。",
                                    description="口语转书面语"
                                )
                            ]
                        ),
                        SubRule(
                            id="sentence_completion",
                            name="句子完整性",
                            description="补全不完整的句子",
                            implementation_type="llm",
                            examples=[
                                RuleExample(
                                    input_text="网络、计算各种丰富性都很好，安全产品线可以再丰富一些。",
                                    output_text="网络、计算产品的丰富性都很好，安全产品线可以再丰富一些。",
                                    description="补全句子"
                                )
                            ]
                        )
                    ]
                )
            ]
        )
    
    @staticmethod
    def get_test_cases_for_category(category_id: str) -> List[str]:
        """获取分类的测试用例"""
        test_cases = {
            "content_filtering": [
                "M：主任您好，华为委托的第三方。\n1：好，您说。",
                "M：想了解一下我们跟华为合作的使用它的产品方案。\n1：嗯，得有一二十年了。"
            ],
            "format_optimization": [
                "1：基本上用了它网络安全什么服务器什么一类的，基本上全用了。",
                "1：网络、计算各种丰富性都很好，安全产品线可以再丰富一些。"
            ],
            "content_organization": [
                "得有一二十年了。基本上用了它网络安全什么服务器什么一类的。价格相较其他要贵不少。",
                "华为的售前特别专业，这一块应该是全国数一数二的。人员这方面我觉得华为总体素质特别高。"
            ],
            "language_optimization": [
                "你像飞腾有，但是鲲鹏的量是最大的，服务器这块儿处理能力比较强。",
                "我觉得华为是大品牌，华为的人特别勤奋，我感觉这一点让人非常赞赏。"
            ]
        }
        return test_cases.get(category_id, [])
