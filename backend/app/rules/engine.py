"""
规则引擎核心
负责规则的执行和文本转换
"""
import re
import time
from typing import List, Dict, Any, Optional, Tuple
from .models import (
    Rule, RuleCondition, RuleAction, RuleExecutionResult, 
    TransformationResult, OperatorType, ActionType
)


class RuleEngine:
    """规则执行引擎"""
    
    def __init__(self):
        self.debug_mode = False
    
    def set_debug_mode(self, enabled: bool):
        """设置调试模式"""
        self.debug_mode = enabled
    
    def execute_rules(self, text: str, rules: List[Rule]) -> TransformationResult:
        """执行规则列表，转换文本"""
        start_time = time.time()
        
        try:
            # 按优先级排序规则
            sorted_rules = sorted(
                [rule for rule in rules if rule.enabled], 
                key=lambda r: r.priority
            )
            
            current_text = text
            execution_results = []
            
            for rule in sorted_rules:
                rule_start_time = time.time()
                
                try:
                    # 执行单个规则
                    result_text, matches, changes = self._execute_single_rule(current_text, rule)
                    
                    rule_execution_time = (time.time() - rule_start_time) * 1000
                    
                    # 记录执行结果
                    execution_result = RuleExecutionResult(
                        rule_id=rule.id,
                        rule_name=rule.name,
                        applied=changes > 0,
                        matches_count=matches,
                        changes_made=changes,
                        execution_time_ms=rule_execution_time
                    )
                    
                    execution_results.append(execution_result)
                    current_text = result_text
                    
                    if self.debug_mode:
                        print(f"规则 {rule.name} 执行完成: {matches} 匹配, {changes} 修改")
                
                except Exception as e:
                    rule_execution_time = (time.time() - rule_start_time) * 1000
                    
                    execution_result = RuleExecutionResult(
                        rule_id=rule.id,
                        rule_name=rule.name,
                        applied=False,
                        matches_count=0,
                        changes_made=0,
                        execution_time_ms=rule_execution_time,
                        error_message=str(e)
                    )
                    
                    execution_results.append(execution_result)
                    
                    if self.debug_mode:
                        print(f"规则 {rule.name} 执行失败: {str(e)}")
            
            total_time = (time.time() - start_time) * 1000
            
            return TransformationResult(
                original_text=text,
                transformed_text=current_text,
                rules_applied=execution_results,
                total_execution_time_ms=total_time,
                success=True
            )
        
        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            
            return TransformationResult(
                original_text=text,
                transformed_text=text,
                rules_applied=[],
                total_execution_time_ms=total_time,
                success=False,
                error_message=str(e)
            )
    
    def _execute_single_rule(self, text: str, rule: Rule) -> Tuple[str, int, int]:
        """执行单个规则"""
        lines = text.split('\n')
        result_lines = []
        total_matches = 0
        total_changes = 0
        
        for line in lines:
            # 检查条件是否匹配
            if self._check_conditions(line, rule.conditions):
                total_matches += 1
                
                # 应用动作
                modified_line, changes = self._apply_actions(line, rule.actions)
                
                if changes > 0:
                    total_changes += changes
                    result_lines.append(modified_line)
                elif not any(action.type == ActionType.REMOVE_LINE for action in rule.actions):
                    result_lines.append(line)
                # 如果是删除行动作且有修改，则不添加该行
            else:
                result_lines.append(line)
        
        return '\n'.join(result_lines), total_matches, total_changes
    
    def _check_conditions(self, line: str, conditions: List[RuleCondition]) -> bool:
        """检查条件是否匹配"""
        if not conditions:
            return True
        
        for condition in conditions:
            if not self._check_single_condition(line, condition):
                return False
        
        return True
    
    def _check_single_condition(self, line: str, condition: RuleCondition) -> bool:
        """检查单个条件"""
        # 获取字段值
        field_value = self._get_field_value(line, condition.field)
        compare_value = str(condition.value)
        
        # 处理大小写敏感性
        if not condition.case_sensitive:
            field_value = field_value.lower()
            compare_value = compare_value.lower()
        
        # 执行比较
        if condition.operator == OperatorType.EQUALS:
            return field_value == compare_value
        elif condition.operator == OperatorType.NOT_EQUALS:
            return field_value != compare_value
        elif condition.operator == OperatorType.CONTAINS:
            return compare_value in field_value
        elif condition.operator == OperatorType.NOT_CONTAINS:
            return compare_value not in field_value
        elif condition.operator == OperatorType.STARTS_WITH:
            return field_value.startswith(compare_value)
        elif condition.operator == OperatorType.ENDS_WITH:
            return field_value.endswith(compare_value)
        elif condition.operator == OperatorType.REGEX_MATCH:
            try:
                flags = 0 if condition.case_sensitive else re.IGNORECASE
                return bool(re.search(compare_value, field_value, flags))
            except re.error:
                return False
        elif condition.operator == OperatorType.LENGTH_GT:
            return len(field_value) > float(condition.value)
        elif condition.operator == OperatorType.LENGTH_LT:
            return len(field_value) < float(condition.value)
        
        return False
    
    def _get_field_value(self, line: str, field: str) -> str:
        """获取字段值"""
        if field == "line":
            return line
        elif field == "speaker":
            # 提取发言人（冒号前的部分）
            if '：' in line:
                return line.split('：')[0].strip()
            elif ':' in line:
                return line.split(':')[0].strip()
            return ""
        elif field == "content":
            # 提取内容（冒号后的部分）
            if '：' in line:
                parts = line.split('：', 1)
                return parts[1].strip() if len(parts) > 1 else ""
            elif ':' in line:
                parts = line.split(':', 1)
                return parts[1].strip() if len(parts) > 1 else ""
            return line
        elif field == "length":
            return str(len(line))
        else:
            return line
    
    def _apply_actions(self, line: str, actions: List[RuleAction]) -> Tuple[str, int]:
        """应用动作列表"""
        current_line = line
        total_changes = 0
        
        for action in actions:
            modified_line, changes = self._apply_single_action(current_line, action)
            if changes > 0:
                current_line = modified_line
                total_changes += changes
        
        return current_line, total_changes
    
    def _apply_single_action(self, line: str, action: RuleAction) -> Tuple[str, int]:
        """应用单个动作"""
        if action.type == ActionType.REMOVE_LINE:
            return "", 1
        
        elif action.type == ActionType.REPLACE_TEXT:
            pattern = action.params.get('pattern', '')
            replacement = action.params.get('replacement', '')
            use_regex = action.params.get('use_regex', False)
            
            if use_regex:
                try:
                    new_line = re.sub(pattern, replacement, line)
                    return new_line, 1 if new_line != line else 0
                except re.error:
                    return line, 0
            else:
                new_line = line.replace(pattern, replacement)
                return new_line, 1 if new_line != line else 0
        
        elif action.type == ActionType.ADD_PREFIX:
            prefix = action.params.get('text', '')
            return prefix + line, 1 if prefix else 0
        
        elif action.type == ActionType.ADD_SUFFIX:
            suffix = action.params.get('text', '')
            return line + suffix, 1 if suffix else 0
        
        elif action.type == ActionType.TRANSFORM_CASE:
            case_type = action.params.get('case_type', 'lower')
            if case_type == 'upper':
                return line.upper(), 1
            elif case_type == 'lower':
                return line.lower(), 1
            elif case_type == 'title':
                return line.title(), 1
            elif case_type == 'capitalize':
                return line.capitalize(), 1
        
        elif action.type == ActionType.EXTRACT_CONTENT:
            # 提取内容（去除发言人标识）
            if '：' in line:
                parts = line.split('：', 1)
                if len(parts) > 1:
                    return parts[1].strip(), 1
            elif ':' in line:
                parts = line.split(':', 1)
                if len(parts) > 1:
                    return parts[1].strip(), 1
            return line, 0
        
        return line, 0
