"""
规则引擎数据模型
定义规则的数据结构和验证逻辑
"""
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, validator
from enum import Enum
import re


class RuleType(str, Enum):
    """规则类型枚举"""
    TEXT_REPLACE = "text_replace"           # 文本替换
    REGEX_REPLACE = "regex_replace"         # 正则替换
    SPEAKER_FILTER = "speaker_filter"       # 发言人过滤
    LINE_FILTER = "line_filter"            # 行过滤
    FORMAT_ADJUST = "format_adjust"         # 格式调整
    CONDITIONAL = "conditional"             # 条件规则


class OperatorType(str, Enum):
    """操作符类型枚举"""
    EQUALS = "equals"                       # 等于
    NOT_EQUALS = "not_equals"              # 不等于
    CONTAINS = "contains"                   # 包含
    NOT_CONTAINS = "not_contains"          # 不包含
    STARTS_WITH = "starts_with"            # 开始于
    ENDS_WITH = "ends_with"                # 结束于
    REGEX_MATCH = "regex_match"            # 正则匹配
    LENGTH_GT = "length_gt"                # 长度大于
    LENGTH_LT = "length_lt"                # 长度小于


class ActionType(str, Enum):
    """动作类型枚举"""
    REMOVE_LINE = "remove_line"            # 删除行
    REPLACE_TEXT = "replace_text"          # 替换文本
    ADD_PREFIX = "add_prefix"              # 添加前缀
    ADD_SUFFIX = "add_suffix"              # 添加后缀
    TRANSFORM_CASE = "transform_case"      # 转换大小写
    EXTRACT_CONTENT = "extract_content"    # 提取内容
    MERGE_LINES = "merge_lines"            # 合并行


class RuleCondition(BaseModel):
    """规则条件"""
    field: str = Field(..., description="条件字段")
    operator: OperatorType = Field(..., description="操作符")
    value: Union[str, int, float] = Field(..., description="比较值")
    case_sensitive: bool = Field(default=True, description="是否区分大小写")
    
    @validator('value')
    def validate_value(cls, v, values):
        """验证值的有效性"""
        if 'operator' in values:
            operator = values['operator']
            if operator in [OperatorType.LENGTH_GT, OperatorType.LENGTH_LT]:
                if not isinstance(v, (int, float)) or v < 0:
                    raise ValueError("长度比较的值必须是非负数")
        return v


class RuleAction(BaseModel):
    """规则动作"""
    type: ActionType = Field(..., description="动作类型")
    params: Dict[str, Any] = Field(default_factory=dict, description="动作参数")
    
    @validator('params')
    def validate_params(cls, v, values):
        """验证参数的有效性"""
        if 'type' in values:
            action_type = values['type']
            
            # 验证替换文本动作的参数
            if action_type == ActionType.REPLACE_TEXT:
                if 'pattern' not in v or 'replacement' not in v:
                    raise ValueError("替换文本动作需要 pattern 和 replacement 参数")
            
            # 验证添加前缀/后缀动作的参数
            elif action_type in [ActionType.ADD_PREFIX, ActionType.ADD_SUFFIX]:
                if 'text' not in v:
                    raise ValueError("添加前缀/后缀动作需要 text 参数")
            
            # 验证大小写转换动作的参数
            elif action_type == ActionType.TRANSFORM_CASE:
                if 'case_type' not in v or v['case_type'] not in ['upper', 'lower', 'title', 'capitalize']:
                    raise ValueError("大小写转换动作需要有效的 case_type 参数")
        
        return v


class Rule(BaseModel):
    """规则定义"""
    id: str = Field(..., description="规则ID")
    name: str = Field(..., description="规则名称")
    description: Optional[str] = Field(None, description="规则描述")
    type: RuleType = Field(..., description="规则类型")
    enabled: bool = Field(default=True, description="是否启用")
    priority: int = Field(default=100, description="优先级(数字越小优先级越高)")
    conditions: List[RuleCondition] = Field(default_factory=list, description="规则条件")
    actions: List[RuleAction] = Field(..., description="规则动作")
    tags: List[str] = Field(default_factory=list, description="规则标签")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
    
    @validator('priority')
    def validate_priority(cls, v):
        """验证优先级"""
        if v < 0 or v > 1000:
            raise ValueError("优先级必须在0-1000之间")
        return v
    
    @validator('actions')
    def validate_actions(cls, v):
        """验证动作列表"""
        if not v:
            raise ValueError("规则必须至少包含一个动作")
        return v


class RuleSet(BaseModel):
    """规则集"""
    id: str = Field(..., description="规则集ID")
    name: str = Field(..., description="规则集名称")
    description: Optional[str] = Field(None, description="规则集描述")
    rules: List[Rule] = Field(default_factory=list, description="规则列表")
    enabled: bool = Field(default=True, description="是否启用")
    is_default: bool = Field(default=False, description="是否为默认规则集")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class RuleExecutionResult(BaseModel):
    """规则执行结果"""
    rule_id: str = Field(..., description="规则ID")
    rule_name: str = Field(..., description="规则名称")
    applied: bool = Field(..., description="是否应用成功")
    matches_count: int = Field(default=0, description="匹配次数")
    changes_made: int = Field(default=0, description="修改次数")
    execution_time_ms: float = Field(default=0.0, description="执行时间(毫秒)")
    error_message: Optional[str] = Field(None, description="错误信息")


class TransformationResult(BaseModel):
    """转换结果"""
    original_text: str = Field(..., description="原始文本")
    transformed_text: str = Field(..., description="转换后文本")
    rules_applied: List[RuleExecutionResult] = Field(default_factory=list, description="应用的规则")
    total_execution_time_ms: float = Field(default=0.0, description="总执行时间(毫秒)")
    success: bool = Field(default=True, description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")


# 预设规则模板
class RuleTemplate(BaseModel):
    """规则模板"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    category: str = Field(..., description="模板分类")
    rule: Rule = Field(..., description="规则定义")
    usage_count: int = Field(default=0, description="使用次数")
    is_builtin: bool = Field(default=False, description="是否为内置模板")


# 规则验证结果
class RuleValidationResult(BaseModel):
    """规则验证结果"""
    valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    suggestions: List[str] = Field(default_factory=list, description="建议列表")
