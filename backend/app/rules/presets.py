"""
预设规则库
包含常用的转换规则模板
"""
from typing import List, Dict
from .models import Rule, RuleCondition, RuleAction, RuleTemplate, RuleType, OperatorType, ActionType


class RulePresets:
    """预设规则库"""
    
    @staticmethod
    def get_all_templates() -> List[RuleTemplate]:
        """获取所有预设规则模板"""
        return [
            RulePresets.get_speaker_filter_template(),
            RulePresets.get_moderator_filter_template(),
            RulePresets.get_content_extraction_template(),
            RulePresets.get_empty_line_removal_template(),
            RulePresets.get_punctuation_cleanup_template(),
            RulePresets.get_first_person_conversion_template(),
            RulePresets.get_formal_language_template(),
            RulePresets.get_time_marker_removal_template(),
        ]
    
    @staticmethod
    def get_speaker_filter_template() -> RuleTemplate:
        """发言人过滤模板"""
        rule = Rule(
            id="preset_speaker_filter",
            name="发言人过滤",
            description="过滤指定发言人的内容",
            type=RuleType.SPEAKER_FILTER,
            priority=10,
            conditions=[
                RuleCondition(
                    field="speaker",
                    operator=OperatorType.EQUALS,
                    value="M",
                    case_sensitive=False
                )
            ],
            actions=[
                RuleAction(
                    type=ActionType.REMOVE_LINE,
                    params={}
                )
            ],
            tags=["发言人", "过滤", "访谈者"]
        )
        
        return RuleTemplate(
            id="template_speaker_filter",
            name="发言人过滤",
            description="过滤访谈者(M)或其他指定发言人的发言内容",
            category="内容过滤",
            rule=rule,
            is_builtin=True
        )
    
    @staticmethod
    def get_moderator_filter_template() -> RuleTemplate:
        """主持人过滤模板"""
        rule = Rule(
            id="preset_moderator_filter",
            name="主持人过滤",
            description="过滤主持人和访谈者的发言",
            type=RuleType.SPEAKER_FILTER,
            priority=10,
            conditions=[
                RuleCondition(
                    field="speaker",
                    operator=OperatorType.REGEX_MATCH,
                    value="^(M|主持人|访谈者|MODERATOR)$",
                    case_sensitive=False
                )
            ],
            actions=[
                RuleAction(
                    type=ActionType.REMOVE_LINE,
                    params={}
                )
            ],
            tags=["主持人", "过滤", "访谈者"]
        )
        
        return RuleTemplate(
            id="template_moderator_filter",
            name="主持人过滤",
            description="过滤主持人、访谈者等非受访者的发言内容",
            category="内容过滤",
            rule=rule,
            is_builtin=True
        )
    
    @staticmethod
    def get_content_extraction_template() -> RuleTemplate:
        """内容提取模板"""
        rule = Rule(
            id="preset_content_extraction",
            name="内容提取",
            description="提取发言内容，去除发言人标识",
            type=RuleType.FORMAT_ADJUST,
            priority=50,
            conditions=[
                RuleCondition(
                    field="line",
                    operator=OperatorType.CONTAINS,
                    value="：",
                    case_sensitive=True
                )
            ],
            actions=[
                RuleAction(
                    type=ActionType.EXTRACT_CONTENT,
                    params={}
                )
            ],
            tags=["内容提取", "格式化", "发言人"]
        )
        
        return RuleTemplate(
            id="template_content_extraction",
            name="内容提取",
            description="提取冒号后的发言内容，去除发言人标识符",
            category="格式调整",
            rule=rule,
            is_builtin=True
        )
    
    @staticmethod
    def get_empty_line_removal_template() -> RuleTemplate:
        """空行删除模板"""
        rule = Rule(
            id="preset_empty_line_removal",
            name="空行删除",
            description="删除空行和只包含空白字符的行",
            type=RuleType.LINE_FILTER,
            priority=90,
            conditions=[
                RuleCondition(
                    field="line",
                    operator=OperatorType.REGEX_MATCH,
                    value="^\\s*$",
                    case_sensitive=True
                )
            ],
            actions=[
                RuleAction(
                    type=ActionType.REMOVE_LINE,
                    params={}
                )
            ],
            tags=["空行", "清理", "格式化"]
        )
        
        return RuleTemplate(
            id="template_empty_line_removal",
            name="空行删除",
            description="删除文档中的空行和只包含空白字符的行",
            category="内容清理",
            rule=rule,
            is_builtin=True
        )
    
    @staticmethod
    def get_punctuation_cleanup_template() -> RuleTemplate:
        """标点符号清理模板"""
        rule = Rule(
            id="preset_punctuation_cleanup",
            name="标点符号清理",
            description="清理多余的标点符号",
            type=RuleType.TEXT_REPLACE,
            priority=80,
            conditions=[],
            actions=[
                RuleAction(
                    type=ActionType.REPLACE_TEXT,
                    params={
                        "pattern": "([。！？])\\1+",
                        "replacement": "\\1",
                        "use_regex": True
                    }
                )
            ],
            tags=["标点符号", "清理", "格式化"]
        )
        
        return RuleTemplate(
            id="template_punctuation_cleanup",
            name="标点符号清理",
            description="清理重复的标点符号，如多个句号、感叹号等",
            category="内容清理",
            rule=rule,
            is_builtin=True
        )
    
    @staticmethod
    def get_first_person_conversion_template() -> RuleTemplate:
        """第一人称转换模板"""
        rule = Rule(
            id="preset_first_person_conversion",
            name="第一人称转换",
            description="将第三人称表述转换为第一人称",
            type=RuleType.TEXT_REPLACE,
            priority=60,
            conditions=[],
            actions=[
                RuleAction(
                    type=ActionType.REPLACE_TEXT,
                    params={
                        "pattern": "他说|她说|该受访者表示|受访者认为",
                        "replacement": "我说",
                        "use_regex": True
                    }
                )
            ],
            tags=["第一人称", "转换", "语言优化"]
        )
        
        return RuleTemplate(
            id="template_first_person_conversion",
            name="第一人称转换",
            description="将第三人称的表述转换为第一人称叙述",
            category="语言优化",
            rule=rule,
            is_builtin=True
        )
    
    @staticmethod
    def get_formal_language_template() -> RuleTemplate:
        """正式语言模板"""
        rule = Rule(
            id="preset_formal_language",
            name="正式语言转换",
            description="将口语化表达转换为正式语言",
            type=RuleType.TEXT_REPLACE,
            priority=70,
            conditions=[],
            actions=[
                RuleAction(
                    type=ActionType.REPLACE_TEXT,
                    params={
                        "pattern": "嗯|啊|呃|那个|这个",
                        "replacement": "",
                        "use_regex": True
                    }
                )
            ],
            tags=["正式语言", "口语化", "语言优化"]
        )
        
        return RuleTemplate(
            id="template_formal_language",
            name="正式语言转换",
            description="去除口语化表达，使语言更加正式",
            category="语言优化",
            rule=rule,
            is_builtin=True
        )
    
    @staticmethod
    def get_time_marker_removal_template() -> RuleTemplate:
        """时间标记删除模板"""
        rule = Rule(
            id="preset_time_marker_removal",
            name="时间标记删除",
            description="删除时间戳和时间标记",
            type=RuleType.TEXT_REPLACE,
            priority=20,
            conditions=[],
            actions=[
                RuleAction(
                    type=ActionType.REPLACE_TEXT,
                    params={
                        "pattern": "\\[\\d{2}:\\d{2}:\\d{2}\\]|\\(\\d{2}:\\d{2}\\)",
                        "replacement": "",
                        "use_regex": True
                    }
                )
            ],
            tags=["时间标记", "清理", "格式化"]
        )
        
        return RuleTemplate(
            id="template_time_marker_removal",
            name="时间标记删除",
            description="删除文本中的时间戳标记，如[12:34:56]或(12:34)",
            category="内容清理",
            rule=rule,
            is_builtin=True
        )
    
    @staticmethod
    def get_default_ruleset() -> List[Rule]:
        """获取默认规则集"""
        templates = RulePresets.get_all_templates()
        return [template.rule for template in templates[:4]]  # 返回前4个常用规则
    
    @staticmethod
    def get_templates_by_category() -> Dict[str, List[RuleTemplate]]:
        """按分类获取模板"""
        templates = RulePresets.get_all_templates()
        categories = {}
        
        for template in templates:
            category = template.category
            if category not in categories:
                categories[category] = []
            categories[category].append(template)
        
        return categories
