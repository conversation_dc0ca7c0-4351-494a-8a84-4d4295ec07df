"""
文件处理服务 - 处理文件上传、解析和下载
"""
import os
import uuid
import aiofiles
import magic
from typing import Optional, Dict, Any, List
from pathlib import Path
from fastapi import UploadFile, HTTPException
from docx import Document
import tempfile
import shutil

from app.core.config import settings


class FileService:
    """文件处理服务类"""
    
    def __init__(self):
        self.upload_path = Path(settings.UPLOAD_PATH)
        self.upload_path.mkdir(exist_ok=True)
        
        # 支持的文件类型
        self.allowed_extensions = set(settings.ALLOWED_EXTENSIONS.split(','))
        self.allowed_mime_types = {
            'text/plain',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
    
    async def upload_file(self, file: UploadFile) -> Dict[str, Any]:
        """
        上传文件并返回文件信息
        
        Args:
            file: 上传的文件对象
            
        Returns:
            文件信息字典
        """
        # 验证文件
        await self._validate_file(file)
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        file_extension = self._get_file_extension(file.filename)
        stored_filename = f"{file_id}{file_extension}"
        file_path = self.upload_path / stored_filename
        
        # 保存文件
        try:
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"文件保存失败: {str(e)}")
        
        # 解析文件内容
        try:
            text_content = await self._extract_text(file_path, file_extension)
        except Exception as e:
            # 清理已上传的文件
            if file_path.exists():
                file_path.unlink()
            raise HTTPException(status_code=400, detail=f"文件解析失败: {str(e)}")
        
        # 返回文件信息
        file_info = {
            "file_id": file_id,
            "original_filename": file.filename,
            "stored_filename": stored_filename,
            "file_size": len(content),
            "file_type": file_extension.lstrip('.'),
            "text_content": text_content,
            "character_count": len(text_content),
            "line_count": len(text_content.split('\n')),
            "upload_path": str(file_path)
        }
        
        return file_info
    
    async def get_file_content(self, file_id: str) -> Optional[str]:
        """
        根据文件ID获取文件内容
        
        Args:
            file_id: 文件ID
            
        Returns:
            文件文本内容
        """
        # 查找文件
        file_path = self._find_file_by_id(file_id)
        if not file_path:
            return None
        
        # 获取文件扩展名
        file_extension = file_path.suffix
        
        # 提取文本内容
        return await self._extract_text(file_path, file_extension)
    
    async def delete_file(self, file_id: str) -> bool:
        """
        删除文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            是否删除成功
        """
        file_path = self._find_file_by_id(file_id)
        if file_path and file_path.exists():
            try:
                file_path.unlink()
                return True
            except Exception:
                return False
        return False
    
    async def create_download_file(self, content: str, filename: str, file_format: str = "txt") -> str:
        """
        创建下载文件
        
        Args:
            content: 文件内容
            filename: 文件名
            file_format: 文件格式 (txt, docx)
            
        Returns:
            下载文件路径
        """
        download_id = str(uuid.uuid4())
        
        if file_format.lower() == "txt":
            download_path = self.upload_path / f"{download_id}_{filename}.txt"
            async with aiofiles.open(download_path, 'w', encoding='utf-8') as f:
                await f.write(content)
        
        elif file_format.lower() == "docx":
            download_path = self.upload_path / f"{download_id}_{filename}.docx"
            await self._create_docx_file(content, download_path)
        
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式")
        
        return str(download_path)
    
    async def _validate_file(self, file: UploadFile) -> None:
        """验证上传文件"""
        # 检查文件大小
        content = await file.read()
        await file.seek(0)  # 重置文件指针
        
        if len(content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413, 
                detail=f"文件大小超过限制 ({settings.MAX_FILE_SIZE} bytes)"
            )
        
        # 检查文件扩展名
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        file_extension = self._get_file_extension(file.filename).lstrip('.')
        if file_extension not in self.allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型。支持的格式: {', '.join(self.allowed_extensions)}"
            )
        
        # 检查MIME类型
        try:
            mime_type = magic.from_buffer(content, mime=True)
            if mime_type not in self.allowed_mime_types:
                raise HTTPException(
                    status_code=400, 
                    detail=f"文件类型验证失败。检测到的类型: {mime_type}"
                )
        except Exception:
            # 如果MIME检测失败，只依赖扩展名验证
            pass
    
    def _get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        return Path(filename).suffix.lower()
    
    def _find_file_by_id(self, file_id: str) -> Optional[Path]:
        """根据文件ID查找文件"""
        for file_path in self.upload_path.glob(f"{file_id}.*"):
            return file_path
        return None
    
    async def _extract_text(self, file_path: Path, file_extension: str) -> str:
        """
        从文件中提取文本内容
        
        Args:
            file_path: 文件路径
            file_extension: 文件扩展名
            
        Returns:
            提取的文本内容
        """
        if file_extension == '.txt':
            return await self._extract_text_from_txt(file_path)
        elif file_extension == '.docx':
            return await self._extract_text_from_docx(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {file_extension}")
    
    async def _extract_text_from_txt(self, file_path: Path) -> str:
        """从TXT文件提取文本"""
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return content
        except UnicodeDecodeError:
            # 尝试其他编码
            for encoding in ['gbk', 'gb2312', 'latin1']:
                try:
                    async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                        content = await f.read()
                        return content
                except UnicodeDecodeError:
                    continue
            raise ValueError("无法解码文件，请检查文件编码")
    
    async def _extract_text_from_docx(self, file_path: Path) -> str:
        """从DOCX文件提取文本"""
        try:
            doc = Document(file_path)
            paragraphs = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    paragraphs.append(paragraph.text)
            
            return '\n'.join(paragraphs)
        except Exception as e:
            raise ValueError(f"DOCX文件解析失败: {str(e)}")
    
    async def _create_docx_file(self, content: str, output_path: Path) -> None:
        """创建DOCX文件"""
        try:
            doc = Document()
            
            # 按段落分割内容
            paragraphs = content.split('\n')
            
            for paragraph_text in paragraphs:
                if paragraph_text.strip():
                    doc.add_paragraph(paragraph_text)
            
            doc.save(output_path)
        except Exception as e:
            raise ValueError(f"DOCX文件创建失败: {str(e)}")


# 创建全局文件服务实例
file_service = FileService()
