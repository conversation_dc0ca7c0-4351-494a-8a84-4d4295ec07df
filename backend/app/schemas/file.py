"""
文件相关的Pydantic模式
"""
from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class FileUploadResponse(BaseModel):
    """文件上传响应模式"""
    file_id: str = Field(..., description="文件唯一ID")
    original_filename: str = Field(..., description="原始文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    file_type: str = Field(..., description="文件类型")
    character_count: int = Field(..., description="字符数")
    line_count: int = Field(..., description="行数")
    text_preview: str = Field(..., description="文本预览(前200字符)")
    upload_time: datetime = Field(..., description="上传时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "file_id": "123e4567-e89b-12d3-a456-426614174000",
                "original_filename": "interview_record.txt",
                "file_size": 2048,
                "file_type": "txt",
                "character_count": 1500,
                "line_count": 45,
                "text_preview": "M：主任您好，华为委托的第三方...",
                "upload_time": "2024-01-01T12:00:00Z"
            }
        }


class FileInfo(BaseModel):
    """文件信息模式"""
    file_id: str = Field(..., description="文件唯一ID")
    original_filename: str = Field(..., description="原始文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    file_type: str = Field(..., description="文件类型")
    character_count: int = Field(..., description="字符数")
    line_count: int = Field(..., description="行数")
    is_processed: bool = Field(..., description="是否已处理")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class FileContent(BaseModel):
    """文件内容模式"""
    file_id: str = Field(..., description="文件唯一ID")
    original_filename: str = Field(..., description="原始文件名")
    text_content: str = Field(..., description="文件文本内容")
    character_count: int = Field(..., description="字符数")
    line_count: int = Field(..., description="行数")


class DownloadRequest(BaseModel):
    """下载请求模式"""
    content: str = Field(..., description="要下载的内容")
    filename: str = Field(..., description="文件名(不含扩展名)")
    file_format: str = Field(default="txt", description="文件格式", regex="^(txt|docx)$")
    
    class Config:
        json_schema_extra = {
            "example": {
                "content": "【访谈内容】\n这是转换后的文档内容...",
                "filename": "converted_interview",
                "file_format": "txt"
            }
        }


class DownloadResponse(BaseModel):
    """下载响应模式"""
    download_id: str = Field(..., description="下载ID")
    filename: str = Field(..., description="下载文件名")
    file_format: str = Field(..., description="文件格式")
    download_url: str = Field(..., description="下载链接")
    expires_at: datetime = Field(..., description="过期时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "download_id": "456e7890-e89b-12d3-a456-426614174000",
                "filename": "converted_interview.txt",
                "file_format": "txt",
                "download_url": "/api/v1/download/456e7890-e89b-12d3-a456-426614174000",
                "expires_at": "2024-01-01T18:00:00Z"
            }
        }


class FileListResponse(BaseModel):
    """文件列表响应模式"""
    files: list[FileInfo] = Field(..., description="文件列表")
    total: int = Field(..., description="文件总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")


class FileProcessingStatus(BaseModel):
    """文件处理状态模式"""
    file_id: str = Field(..., description="文件ID")
    status: str = Field(..., description="处理状态")
    progress: Optional[float] = Field(None, description="处理进度", ge=0, le=100)
    message: Optional[str] = Field(None, description="状态消息")
    error_message: Optional[str] = Field(None, description="错误信息")


class FileValidationError(BaseModel):
    """文件验证错误模式"""
    error_type: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[dict] = Field(None, description="错误详情")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error_type": "file_size_exceeded",
                "message": "文件大小超过限制",
                "details": {
                    "max_size": 10485760,
                    "actual_size": 15728640
                }
            }
        }
