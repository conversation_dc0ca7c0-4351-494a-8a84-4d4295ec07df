"""
规则相关的API Schema定义
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from ..rules.models import Rule, RuleSet, RuleTemplate, TransformationResult


class RuleCreateRequest(BaseModel):
    """创建规则请求"""
    rule: Rule


class RuleUpdateRequest(BaseModel):
    """更新规则请求"""
    rule: Rule


class RuleTestRequest(BaseModel):
    """规则测试请求"""
    rule: Rule
    test_text: str = Field(..., description="测试文本")


class RuleExecuteRequest(BaseModel):
    """规则执行请求"""
    text: str = Field(..., description="要转换的文本")
    rule_ids: Optional[List[str]] = Field(None, description="指定的规则ID列表")
    ruleset_id: Optional[str] = Field(None, description="规则集ID")
    use_default_rules: bool = Field(True, description="是否使用默认规则")


class RuleSetCreateRequest(BaseModel):
    """创建规则集请求"""
    ruleset: RuleSet


class RuleSetUpdateRequest(BaseModel):
    """更新规则集请求"""
    ruleset: RuleSet


class RuleListResponse(BaseModel):
    """规则列表响应"""
    rules: List[Rule]
    total: int
    page: int = 1
    page_size: int = 20


class RuleTemplateListResponse(BaseModel):
    """规则模板列表响应"""
    templates: List[RuleTemplate]
    categories: Dict[str, List[RuleTemplate]]


class RuleExecutionResponse(BaseModel):
    """规则执行响应"""
    result: TransformationResult
    success: bool
    message: str = ""


class RuleValidationResponse(BaseModel):
    """规则验证响应"""
    valid: bool
    errors: List[str] = []
    warnings: List[str] = []
    suggestions: List[str] = []


class RuleImportRequest(BaseModel):
    """规则导入请求"""
    template_id: str = Field(..., description="模板ID")
    custom_name: Optional[str] = Field(None, description="自定义规则名称")


class RuleExportResponse(BaseModel):
    """规则导出响应"""
    rules: List[Rule]
    export_format: str = "json"
    export_time: str
