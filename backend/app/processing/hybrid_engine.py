"""
混合处理引擎
结合规则引擎和LLM的智能转换系统
"""
import time
from typing import Dict, Any, Optional, List
from enum import Enum

from ..rules.engine import RuleEngine
from ..rules.models import Rule, TransformationResult as RuleTransformationResult
from ..rules.presets import RulePresets


class ProcessingMode(str, Enum):
    """处理模式枚举"""
    RULES_ONLY = "rules_only"          # 仅使用规则引擎
    LLM_ONLY = "llm_only"              # 仅使用LLM
    HYBRID = "hybrid"                   # 混合处理
    RULES_THEN_LLM = "rules_then_llm"  # 先规则后LLM
    LLM_THEN_RULES = "llm_then_rules"  # 先LLM后规则


class HybridTransformationResult:
    """混合处理结果"""
    def __init__(self):
        self.original_text: str = ""
        self.final_text: str = ""
        self.processing_mode: ProcessingMode = ProcessingMode.RULES_ONLY
        self.rules_result: Optional[RuleTransformationResult] = None
        self.llm_result: Optional[Dict[str, Any]] = None
        self.total_execution_time_ms: float = 0.0
        self.success: bool = True
        self.error_message: Optional[str] = None
        self.quality_metrics: Dict[str, float] = {}
        self.processing_steps: List[Dict[str, Any]] = []


class HybridProcessingEngine:
    """混合处理引擎"""
    
    def __init__(self):
        self.rule_engine = RuleEngine()
        self.llm_enabled = False  # 暂时禁用LLM，使用模拟
        
    def set_llm_enabled(self, enabled: bool):
        """设置LLM是否启用"""
        self.llm_enabled = enabled
    
    def process_text(
        self, 
        text: str, 
        mode: ProcessingMode = ProcessingMode.RULES_ONLY,
        custom_rules: Optional[List[Rule]] = None,
        llm_provider: str = "mock"
    ) -> HybridTransformationResult:
        """
        处理文本
        
        Args:
            text: 输入文本
            mode: 处理模式
            custom_rules: 自定义规则列表
            llm_provider: LLM提供商
            
        Returns:
            HybridTransformationResult: 处理结果
        """
        start_time = time.time()
        result = HybridTransformationResult()
        result.original_text = text
        result.processing_mode = mode
        
        try:
            if mode == ProcessingMode.RULES_ONLY:
                result = self._process_rules_only(text, custom_rules, result)
            elif mode == ProcessingMode.LLM_ONLY:
                result = self._process_llm_only(text, llm_provider, result)
            elif mode == ProcessingMode.HYBRID:
                result = self._process_hybrid(text, custom_rules, llm_provider, result)
            elif mode == ProcessingMode.RULES_THEN_LLM:
                result = self._process_rules_then_llm(text, custom_rules, llm_provider, result)
            elif mode == ProcessingMode.LLM_THEN_RULES:
                result = self._process_llm_then_rules(text, custom_rules, llm_provider, result)
            else:
                raise ValueError(f"不支持的处理模式: {mode}")
            
            result.total_execution_time_ms = (time.time() - start_time) * 1000
            result.success = True
            
        except Exception as e:
            result.total_execution_time_ms = (time.time() - start_time) * 1000
            result.success = False
            result.error_message = str(e)
            result.final_text = text  # 失败时返回原文
        
        return result
    
    def _process_rules_only(
        self, 
        text: str, 
        custom_rules: Optional[List[Rule]], 
        result: HybridTransformationResult
    ) -> HybridTransformationResult:
        """仅使用规则引擎处理"""
        rules = custom_rules if custom_rules else RulePresets.get_default_ruleset()
        
        rule_result = self.rule_engine.execute_rules(text, rules)
        result.rules_result = rule_result
        result.final_text = rule_result.transformed_text
        
        result.processing_steps.append({
            "step": "rules_processing",
            "input_length": len(text),
            "output_length": len(rule_result.transformed_text),
            "rules_applied": len(rule_result.rules_applied),
            "execution_time_ms": rule_result.total_execution_time_ms
        })
        
        return result
    
    def _process_llm_only(
        self, 
        text: str, 
        llm_provider: str, 
        result: HybridTransformationResult
    ) -> HybridTransformationResult:
        """仅使用LLM处理"""
        if self.llm_enabled:
            # 实际LLM调用逻辑
            llm_result = self._call_llm(text, llm_provider)
        else:
            # 模拟LLM处理
            llm_result = self._mock_llm_processing(text)
        
        result.llm_result = llm_result
        result.final_text = llm_result.get("transformed_text", text)
        
        result.processing_steps.append({
            "step": "llm_processing",
            "input_length": len(text),
            "output_length": len(result.final_text),
            "llm_provider": llm_provider,
            "execution_time_ms": llm_result.get("execution_time_ms", 0)
        })
        
        return result
    
    def _process_hybrid(
        self, 
        text: str, 
        custom_rules: Optional[List[Rule]], 
        llm_provider: str, 
        result: HybridTransformationResult
    ) -> HybridTransformationResult:
        """混合处理：规则引擎预处理 + LLM精细化"""
        # 第一步：规则引擎预处理
        rules = custom_rules if custom_rules else RulePresets.get_default_ruleset()
        rule_result = self.rule_engine.execute_rules(text, rules)
        result.rules_result = rule_result
        
        result.processing_steps.append({
            "step": "rules_preprocessing",
            "input_length": len(text),
            "output_length": len(rule_result.transformed_text),
            "rules_applied": len(rule_result.rules_applied),
            "execution_time_ms": rule_result.total_execution_time_ms
        })
        
        # 第二步：LLM精细化处理
        if self.llm_enabled:
            llm_result = self._call_llm(rule_result.transformed_text, llm_provider)
        else:
            llm_result = self._mock_llm_processing(rule_result.transformed_text)
        
        result.llm_result = llm_result
        result.final_text = llm_result.get("transformed_text", rule_result.transformed_text)
        
        result.processing_steps.append({
            "step": "llm_refinement",
            "input_length": len(rule_result.transformed_text),
            "output_length": len(result.final_text),
            "llm_provider": llm_provider,
            "execution_time_ms": llm_result.get("execution_time_ms", 0)
        })
        
        return result
    
    def _process_rules_then_llm(
        self, 
        text: str, 
        custom_rules: Optional[List[Rule]], 
        llm_provider: str, 
        result: HybridTransformationResult
    ) -> HybridTransformationResult:
        """先规则后LLM处理"""
        return self._process_hybrid(text, custom_rules, llm_provider, result)
    
    def _process_llm_then_rules(
        self, 
        text: str, 
        custom_rules: Optional[List[Rule]], 
        llm_provider: str, 
        result: HybridTransformationResult
    ) -> HybridTransformationResult:
        """先LLM后规则处理"""
        # 第一步：LLM处理
        if self.llm_enabled:
            llm_result = self._call_llm(text, llm_provider)
        else:
            llm_result = self._mock_llm_processing(text)
        
        result.llm_result = llm_result
        llm_text = llm_result.get("transformed_text", text)
        
        result.processing_steps.append({
            "step": "llm_processing",
            "input_length": len(text),
            "output_length": len(llm_text),
            "llm_provider": llm_provider,
            "execution_time_ms": llm_result.get("execution_time_ms", 0)
        })
        
        # 第二步：规则引擎后处理
        rules = custom_rules if custom_rules else RulePresets.get_default_ruleset()
        rule_result = self.rule_engine.execute_rules(llm_text, rules)
        result.rules_result = rule_result
        result.final_text = rule_result.transformed_text
        
        result.processing_steps.append({
            "step": "rules_postprocessing",
            "input_length": len(llm_text),
            "output_length": len(rule_result.transformed_text),
            "rules_applied": len(rule_result.rules_applied),
            "execution_time_ms": rule_result.total_execution_time_ms
        })
        
        return result
    
    def _call_llm(self, text: str, provider: str) -> Dict[str, Any]:
        """调用实际LLM服务"""
        # TODO: 实现真实的LLM调用
        # 这里应该根据provider调用相应的LLM服务
        return self._mock_llm_processing(text)
    
    def _mock_llm_processing(self, text: str) -> Dict[str, Any]:
        """模拟LLM处理"""
        start_time = time.time()
        
        # 简单的模拟处理：添加一些语言优化
        lines = text.split('\n')
        processed_lines = []
        
        for line in lines:
            if not line.strip():
                continue
            
            # 模拟LLM的语言优化
            processed_line = line
            processed_line = processed_line.replace("嗯", "")
            processed_line = processed_line.replace("啊", "")
            processed_line = processed_line.replace("那个", "")
            processed_line = processed_line.replace("这个", "")
            processed_line = processed_line.strip()
            
            if processed_line:
                processed_lines.append(processed_line)
        
        result_text = '\n'.join(processed_lines)
        execution_time = (time.time() - start_time) * 1000
        
        return {
            "transformed_text": result_text,
            "execution_time_ms": execution_time,
            "model": "mock_llm",
            "tokens_used": len(text.split()),
            "confidence": 0.85
        }
    
    def calculate_quality_metrics(self, original: str, transformed: str) -> Dict[str, float]:
        """计算质量指标"""
        if not original or not transformed:
            return {}
        
        # 字数保留率
        original_chars = len(original.replace(' ', '').replace('\n', ''))
        transformed_chars = len(transformed.replace(' ', '').replace('\n', ''))
        char_retention_rate = transformed_chars / original_chars if original_chars > 0 else 0
        
        # 行数保留率
        original_lines = len([line for line in original.split('\n') if line.strip()])
        transformed_lines = len([line for line in transformed.split('\n') if line.strip()])
        line_retention_rate = transformed_lines / original_lines if original_lines > 0 else 0
        
        # 内容相似度（简单实现）
        original_words = set(original.replace('\n', ' ').split())
        transformed_words = set(transformed.replace('\n', ' ').split())
        if original_words:
            content_similarity = len(original_words & transformed_words) / len(original_words)
        else:
            content_similarity = 0
        
        return {
            "char_retention_rate": min(char_retention_rate, 1.0),
            "line_retention_rate": min(line_retention_rate, 1.0),
            "content_similarity": content_similarity,
            "compression_ratio": 1 - char_retention_rate if char_retention_rate < 1 else 0
        }
