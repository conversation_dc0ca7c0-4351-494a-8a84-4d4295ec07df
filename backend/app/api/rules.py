"""
规则管理API接口
提供规则的CRUD操作和规则执行功能
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from ..rules.models import Rule, RuleSet, RuleTemplate, TransformationResult, RuleValidationResult
from ..rules.engine import RuleEngine
from ..rules.presets import RulePresets

router = APIRouter(prefix="/api/v1/rules", tags=["规则管理"])

# 模拟数据存储（实际项目中应使用数据库）
rules_storage: Dict[str, Rule] = {}
rulesets_storage: Dict[str, RuleSet] = {}

# 初始化规则引擎
rule_engine = RuleEngine()


class RuleCreateRequest(BaseModel):
    """创建规则请求"""
    rule: Rule


class RuleUpdateRequest(BaseModel):
    """更新规则请求"""
    rule: Rule


class RuleTestRequest(BaseModel):
    """规则测试请求"""
    rule: Rule
    test_text: str


class RuleExecuteRequest(BaseModel):
    """规则执行请求"""
    text: str
    rule_ids: Optional[List[str]] = None
    ruleset_id: Optional[str] = None


@router.get("/templates", response_model=List[RuleTemplate])
async def get_rule_templates():
    """获取所有规则模板"""
    try:
        templates = RulePresets.get_all_templates()
        return templates
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取规则模板失败: {str(e)}")


@router.get("/templates/categories")
async def get_template_categories():
    """获取规则模板分类"""
    try:
        categories = RulePresets.get_templates_by_category()
        return categories
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板分类失败: {str(e)}")


@router.get("/", response_model=List[Rule])
async def get_rules(
    enabled_only: bool = Query(False, description="只返回启用的规则"),
    tag: Optional[str] = Query(None, description="按标签过滤")
):
    """获取规则列表"""
    try:
        rules = list(rules_storage.values())
        
        if enabled_only:
            rules = [rule for rule in rules if rule.enabled]
        
        if tag:
            rules = [rule for rule in rules if tag in rule.tags]
        
        # 按优先级排序
        rules.sort(key=lambda r: r.priority)
        
        return rules
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取规则列表失败: {str(e)}")


@router.get("/{rule_id}", response_model=Rule)
async def get_rule(rule_id: str):
    """获取单个规则"""
    if rule_id not in rules_storage:
        raise HTTPException(status_code=404, detail="规则不存在")
    
    return rules_storage[rule_id]


@router.post("/", response_model=Rule)
async def create_rule(request: RuleCreateRequest):
    """创建新规则"""
    try:
        rule = request.rule
        
        # 检查规则ID是否已存在
        if rule.id in rules_storage:
            raise HTTPException(status_code=400, detail="规则ID已存在")
        
        # 验证规则
        validation_result = validate_rule(rule)
        if not validation_result.valid:
            raise HTTPException(status_code=400, detail=f"规则验证失败: {', '.join(validation_result.errors)}")
        
        # 保存规则
        rules_storage[rule.id] = rule
        
        return rule
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建规则失败: {str(e)}")


@router.put("/{rule_id}", response_model=Rule)
async def update_rule(rule_id: str, request: RuleUpdateRequest):
    """更新规则"""
    try:
        if rule_id not in rules_storage:
            raise HTTPException(status_code=404, detail="规则不存在")
        
        rule = request.rule
        rule.id = rule_id  # 确保ID一致
        
        # 验证规则
        validation_result = validate_rule(rule)
        if not validation_result.valid:
            raise HTTPException(status_code=400, detail=f"规则验证失败: {', '.join(validation_result.errors)}")
        
        # 更新规则
        rules_storage[rule_id] = rule
        
        return rule
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新规则失败: {str(e)}")


@router.delete("/{rule_id}")
async def delete_rule(rule_id: str):
    """删除规则"""
    if rule_id not in rules_storage:
        raise HTTPException(status_code=404, detail="规则不存在")
    
    del rules_storage[rule_id]
    
    return {"message": "规则删除成功"}


@router.post("/test", response_model=TransformationResult)
async def test_rule(request: RuleTestRequest):
    """测试单个规则"""
    try:
        rule = request.rule
        test_text = request.test_text
        
        # 验证规则
        validation_result = validate_rule(rule)
        if not validation_result.valid:
            raise HTTPException(status_code=400, detail=f"规则验证失败: {', '.join(validation_result.errors)}")
        
        # 执行规则测试
        result = rule_engine.execute_rules(test_text, [rule])
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"规则测试失败: {str(e)}")


@router.post("/execute", response_model=TransformationResult)
async def execute_rules(request: RuleExecuteRequest):
    """执行规则转换"""
    try:
        text = request.text
        rules_to_execute = []
        
        if request.rule_ids:
            # 使用指定的规则ID列表
            for rule_id in request.rule_ids:
                if rule_id in rules_storage:
                    rules_to_execute.append(rules_storage[rule_id])
        elif request.ruleset_id:
            # 使用规则集
            if request.ruleset_id in rulesets_storage:
                ruleset = rulesets_storage[request.ruleset_id]
                rules_to_execute = ruleset.rules
            else:
                raise HTTPException(status_code=404, detail="规则集不存在")
        else:
            # 使用所有启用的规则
            rules_to_execute = [rule for rule in rules_storage.values() if rule.enabled]
        
        if not rules_to_execute:
            raise HTTPException(status_code=400, detail="没有可执行的规则")
        
        # 执行规则转换
        result = rule_engine.execute_rules(text, rules_to_execute)
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"规则执行失败: {str(e)}")


@router.post("/validate", response_model=RuleValidationResult)
async def validate_rule_endpoint(rule: Rule):
    """验证规则"""
    try:
        result = validate_rule(rule)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"规则验证失败: {str(e)}")


@router.post("/import-template/{template_id}", response_model=Rule)
async def import_template(template_id: str):
    """从模板导入规则"""
    try:
        templates = RulePresets.get_all_templates()
        template = next((t for t in templates if t.id == template_id), None)
        
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")
        
        # 生成新的规则ID
        import uuid
        new_rule = template.rule.copy()
        new_rule.id = f"rule_{uuid.uuid4().hex[:8]}"
        
        # 保存规则
        rules_storage[new_rule.id] = new_rule
        
        return new_rule
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入模板失败: {str(e)}")


def validate_rule(rule: Rule) -> RuleValidationResult:
    """验证规则的有效性"""
    errors = []
    warnings = []
    suggestions = []
    
    # 基本验证
    if not rule.name.strip():
        errors.append("规则名称不能为空")
    
    if not rule.actions:
        errors.append("规则必须包含至少一个动作")
    
    # 验证条件
    for i, condition in enumerate(rule.conditions):
        if condition.operator == "regex_match":
            try:
                import re
                re.compile(str(condition.value))
            except re.error as e:
                errors.append(f"条件 {i+1} 的正则表达式无效: {str(e)}")
    
    # 验证动作
    for i, action in enumerate(rule.actions):
        if action.type == "replace_text":
            if "pattern" not in action.params:
                errors.append(f"动作 {i+1} 缺少 pattern 参数")
            elif action.params.get("use_regex", False):
                try:
                    import re
                    re.compile(action.params["pattern"])
                except re.error as e:
                    errors.append(f"动作 {i+1} 的正则表达式无效: {str(e)}")
    
    # 性能建议
    if rule.priority > 500:
        suggestions.append("建议将重要规则的优先级设置得更低（数字更小）")
    
    if len(rule.conditions) > 5:
        warnings.append("条件过多可能影响性能")
    
    return RuleValidationResult(
        valid=len(errors) == 0,
        errors=errors,
        warnings=warnings,
        suggestions=suggestions
    )


# 初始化一些默认规则
def init_default_rules():
    """初始化默认规则"""
    default_rules = RulePresets.get_default_ruleset()
    for rule in default_rules:
        rules_storage[rule.id] = rule


# 启动时初始化
init_default_rules()
