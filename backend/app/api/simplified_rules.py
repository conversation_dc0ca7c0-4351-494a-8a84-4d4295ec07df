"""
简化的规则管理API
提供层级化的规则管理接口
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ..rules.simplified_rules import (
    RuleSet, RuleCategory, MainRule, SubRule, RuleExample,
    SimplifiedRulePresets
)

router = APIRouter(prefix="/api/v1/simple-rules", tags=["简化规则管理"])

# 模拟数据存储
current_ruleset: RuleSet = SimplifiedRulePresets.get_default_ruleset()
saved_rulesets: Dict[str, RuleSet] = {
    "default_ruleset": current_ruleset
}


class RuleSetSaveRequest(BaseModel):
    """保存规则集请求"""
    name: str
    description: str


class SubRuleTestRequest(BaseModel):
    """子规则测试请求"""
    sub_rule_id: str
    test_text: str


class SubRuleTestResult(BaseModel):
    """子规则测试结果"""
    original_text: str
    transformed_text: str
    rule_applied: bool
    execution_time_ms: float


@router.get("/current", response_model=RuleSet)
async def get_current_ruleset():
    """获取当前规则集"""
    return current_ruleset


@router.get("/saved", response_model=List[Dict[str, str]])
async def get_saved_rulesets():
    """获取已保存的规则集列表"""
    return [
        {
            "id": ruleset.id,
            "name": ruleset.name,
            "description": ruleset.description,
            "is_default": ruleset.is_default
        }
        for ruleset in saved_rulesets.values()
    ]


@router.post("/switch/{ruleset_id}")
async def switch_ruleset(ruleset_id: str):
    """切换规则集"""
    global current_ruleset
    
    if ruleset_id not in saved_rulesets:
        raise HTTPException(status_code=404, detail="规则集不存在")
    
    current_ruleset = saved_rulesets[ruleset_id]
    return {"message": f"已切换到规则集: {current_ruleset.name}"}


@router.post("/save", response_model=Dict[str, str])
async def save_current_ruleset(request: RuleSetSaveRequest):
    """保存当前规则集为新规则集"""
    import uuid
    
    new_id = f"ruleset_{uuid.uuid4().hex[:8]}"
    new_ruleset = RuleSet(
        id=new_id,
        name=request.name,
        description=request.description,
        is_default=False,
        categories=current_ruleset.categories.copy()
    )
    
    saved_rulesets[new_id] = new_ruleset
    
    return {
        "id": new_id,
        "name": request.name,
        "message": "规则集保存成功"
    }


@router.put("/category/{category_id}/enabled")
async def toggle_category_enabled(category_id: str, enabled: bool):
    """切换规则大类的启用状态"""
    global current_ruleset
    
    for category in current_ruleset.categories:
        if category.id == category_id:
            category.enabled = enabled
            return {"message": f"规则大类 {category.name} 已{'启用' if enabled else '禁用'}"}
    
    raise HTTPException(status_code=404, detail="规则大类不存在")


@router.put("/main-rule/{main_rule_id}/enabled")
async def toggle_main_rule_enabled(main_rule_id: str, enabled: bool):
    """切换一级规则的启用状态"""
    global current_ruleset
    
    for category in current_ruleset.categories:
        for main_rule in category.main_rules:
            if main_rule.id == main_rule_id:
                main_rule.enabled = enabled
                return {"message": f"一级规则 {main_rule.name} 已{'启用' if enabled else '禁用'}"}
    
    raise HTTPException(status_code=404, detail="一级规则不存在")


@router.put("/sub-rule/{sub_rule_id}/enabled")
async def toggle_sub_rule_enabled(sub_rule_id: str, enabled: bool):
    """切换二级规则的启用状态"""
    global current_ruleset
    
    for category in current_ruleset.categories:
        for main_rule in category.main_rules:
            for sub_rule in main_rule.sub_rules:
                if sub_rule.id == sub_rule_id:
                    sub_rule.enabled = enabled
                    return {"message": f"二级规则 {sub_rule.name} 已{'启用' if enabled else '禁用'}"}
    
    raise HTTPException(status_code=404, detail="二级规则不存在")


@router.get("/sub-rule/{sub_rule_id}/examples")
async def get_sub_rule_examples(sub_rule_id: str):
    """获取子规则的转换示例"""
    for category in current_ruleset.categories:
        for main_rule in category.main_rules:
            for sub_rule in main_rule.sub_rules:
                if sub_rule.id == sub_rule_id:
                    return {
                        "sub_rule": {
                            "id": sub_rule.id,
                            "name": sub_rule.name,
                            "description": sub_rule.description,
                            "implementation_type": sub_rule.implementation_type
                        },
                        "examples": sub_rule.examples
                    }
    
    raise HTTPException(status_code=404, detail="子规则不存在")


@router.post("/sub-rule/test", response_model=SubRuleTestResult)
async def test_sub_rule(request: SubRuleTestRequest):
    """测试单个子规则"""
    import time
    
    # 查找子规则
    target_sub_rule = None
    for category in current_ruleset.categories:
        for main_rule in category.main_rules:
            for sub_rule in main_rule.sub_rules:
                if sub_rule.id == request.sub_rule_id:
                    target_sub_rule = sub_rule
                    break
    
    if not target_sub_rule:
        raise HTTPException(status_code=404, detail="子规则不存在")
    
    start_time = time.time()
    
    # 模拟规则执行
    transformed_text = _apply_sub_rule(request.test_text, target_sub_rule)
    
    execution_time = (time.time() - start_time) * 1000
    
    return SubRuleTestResult(
        original_text=request.test_text,
        transformed_text=transformed_text,
        rule_applied=transformed_text != request.test_text,
        execution_time_ms=execution_time
    )


@router.get("/test-cases/{category_id}")
async def get_test_cases(category_id: str):
    """获取分类的测试用例"""
    test_cases = SimplifiedRulePresets.get_test_cases_for_category(category_id)
    
    if not test_cases:
        raise HTTPException(status_code=404, detail="分类不存在或没有测试用例")
    
    return {
        "category_id": category_id,
        "test_cases": test_cases
    }


@router.get("/statistics")
async def get_rule_statistics():
    """获取规则统计信息"""
    total_categories = len(current_ruleset.categories)
    enabled_categories = sum(1 for cat in current_ruleset.categories if cat.enabled)
    
    total_main_rules = sum(len(cat.main_rules) for cat in current_ruleset.categories)
    enabled_main_rules = sum(
        sum(1 for rule in cat.main_rules if rule.enabled)
        for cat in current_ruleset.categories
    )
    
    total_sub_rules = sum(
        sum(len(rule.sub_rules) for rule in cat.main_rules)
        for cat in current_ruleset.categories
    )
    enabled_sub_rules = sum(
        sum(
            sum(1 for sub_rule in rule.sub_rules if sub_rule.enabled)
            for rule in cat.main_rules
        )
        for cat in current_ruleset.categories
    )
    
    return {
        "ruleset_name": current_ruleset.name,
        "categories": {
            "total": total_categories,
            "enabled": enabled_categories
        },
        "main_rules": {
            "total": total_main_rules,
            "enabled": enabled_main_rules
        },
        "sub_rules": {
            "total": total_sub_rules,
            "enabled": enabled_sub_rules
        }
    }


def _apply_sub_rule(text: str, sub_rule: SubRule) -> str:
    """应用单个子规则（简化实现）"""
    if not sub_rule.enabled:
        return text
    
    # 根据规则ID进行简单的转换
    if sub_rule.id == "moderator_filter":
        lines = text.split('\n')
        filtered_lines = [line for line in lines if not line.strip().startswith('M：')]
        return '\n'.join(filtered_lines)
    
    elif sub_rule.id == "speaker_prefix_removal":
        import re
        return re.sub(r'^\d+：', '', text)
    
    elif sub_rule.id == "filler_words":
        import re
        return re.sub(r'[嗯啊那个这个]+[，,]?\s*', '', text)
    
    elif sub_rule.id == "question_filter":
        lines = text.split('\n')
        filtered_lines = [line for line in lines if not line.strip().startswith('顾问：')]
        return '\n'.join(filtered_lines)
    
    # 对于LLM类型的规则，返回模拟结果
    elif sub_rule.implementation_type == "llm":
        return f"[LLM处理] {text}"
    
    # 默认返回原文
    return text
