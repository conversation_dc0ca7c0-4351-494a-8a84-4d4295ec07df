"""
文件处理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime, timedelta
import os

from app.core.database import get_db
from app.schemas.file import (
    FileUploadResponse,
    FileInfo,
    FileContent,
    DownloadRequest,
    DownloadResponse,
    FileListResponse,
    FileProcessingStatus
)
from app.services.file_service import file_service
from app.models.file import FileRecord, DownloadRecord

router = APIRouter()


@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    上传文件
    
    支持的文件格式：
    - .txt (文本文件)
    - .docx (Word文档)
    
    文件大小限制：10MB
    """
    try:
        # 使用文件服务上传文件
        file_info = await file_service.upload_file(file)
        
        # 保存文件记录到数据库
        file_record = FileRecord(
            file_id=file_info["file_id"],
            original_filename=file_info["original_filename"],
            stored_filename=file_info["stored_filename"],
            file_size=file_info["file_size"],
            file_type=file_info["file_type"],
            text_content=file_info["text_content"],
            character_count=file_info["character_count"],
            line_count=file_info["line_count"],
            upload_path=file_info["upload_path"]
        )
        
        db.add(file_record)
        db.commit()
        db.refresh(file_record)
        
        # 构建响应
        text_preview = file_info["text_content"][:200] + "..." if len(file_info["text_content"]) > 200 else file_info["text_content"]
        
        return FileUploadResponse(
            file_id=file_info["file_id"],
            original_filename=file_info["original_filename"],
            file_size=file_info["file_size"],
            file_type=file_info["file_type"],
            character_count=file_info["character_count"],
            line_count=file_info["line_count"],
            text_preview=text_preview,
            upload_time=file_record.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@router.get("/list", response_model=FileListResponse)
async def list_files(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: Session = Depends(get_db)
):
    """
    获取文件列表
    
    支持分页查询
    """
    try:
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询文件记录
        query = db.query(FileRecord).filter(FileRecord.is_deleted == False)
        total = query.count()
        files = query.offset(offset).limit(page_size).all()
        
        # 构建响应
        file_list = [
            FileInfo(
                file_id=file.file_id,
                original_filename=file.original_filename,
                file_size=file.file_size,
                file_type=file.file_type,
                character_count=file.character_count or 0,
                line_count=file.line_count or 0,
                is_processed=file.is_processed,
                created_at=file.created_at
            )
            for file in files
        ]
        
        return FileListResponse(
            files=file_list,
            total=total,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")


@router.get("/{file_id}", response_model=FileContent)
async def get_file_content(
    file_id: str,
    db: Session = Depends(get_db)
):
    """
    获取文件内容
    
    根据文件ID获取完整的文件文本内容
    """
    try:
        # 从数据库查询文件记录
        file_record = db.query(FileRecord).filter(
            FileRecord.file_id == file_id,
            FileRecord.is_deleted == False
        ).first()
        
        if not file_record:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 获取文件内容
        if file_record.text_content:
            text_content = file_record.text_content
        else:
            # 如果数据库中没有内容，从文件系统读取
            text_content = await file_service.get_file_content(file_id)
            if text_content is None:
                raise HTTPException(status_code=404, detail="文件内容不存在")
        
        return FileContent(
            file_id=file_record.file_id,
            original_filename=file_record.original_filename,
            text_content=text_content,
            character_count=len(text_content),
            line_count=len(text_content.split('\n'))
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件内容失败: {str(e)}")


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    db: Session = Depends(get_db)
):
    """
    删除文件
    
    软删除文件记录，并删除物理文件
    """
    try:
        # 查询文件记录
        file_record = db.query(FileRecord).filter(
            FileRecord.file_id == file_id,
            FileRecord.is_deleted == False
        ).first()
        
        if not file_record:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 软删除数据库记录
        file_record.is_deleted = True
        db.commit()
        
        # 删除物理文件
        await file_service.delete_file(file_id)
        
        return {"message": "文件删除成功", "file_id": file_id}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")


@router.post("/download", response_model=DownloadResponse)
async def create_download(
    request: DownloadRequest,
    db: Session = Depends(get_db)
):
    """
    创建下载文件
    
    根据内容创建可下载的文件
    """
    try:
        # 创建下载文件
        file_path = await file_service.create_download_file(
            content=request.content,
            filename=request.filename,
            file_format=request.file_format
        )
        
        # 生成下载ID
        import uuid
        download_id = str(uuid.uuid4())
        
        # 保存下载记录
        download_record = DownloadRecord(
            download_id=download_id,
            filename=f"{request.filename}.{request.file_format}",
            file_format=request.file_format,
            file_path=file_path
        )
        
        db.add(download_record)
        db.commit()
        
        # 设置过期时间（24小时后）
        expires_at = datetime.now() + timedelta(hours=24)
        
        return DownloadResponse(
            download_id=download_id,
            filename=f"{request.filename}.{request.file_format}",
            file_format=request.file_format,
            download_url=f"/api/v1/file/download/{download_id}",
            expires_at=expires_at
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建下载文件失败: {str(e)}")


@router.get("/download/{download_id}")
async def download_file(
    download_id: str,
    db: Session = Depends(get_db)
):
    """
    下载文件
    
    根据下载ID下载文件
    """
    try:
        # 查询下载记录
        download_record = db.query(DownloadRecord).filter(
            DownloadRecord.download_id == download_id,
            DownloadRecord.is_expired == False
        ).first()
        
        if not download_record:
            raise HTTPException(status_code=404, detail="下载链接不存在或已过期")
        
        # 检查文件是否存在
        if not os.path.exists(download_record.file_path):
            raise HTTPException(status_code=404, detail="下载文件不存在")
        
        # 更新下载记录
        download_record.download_count += 1
        download_record.last_downloaded_at = datetime.now()
        db.commit()
        
        # 返回文件
        return FileResponse(
            path=download_record.file_path,
            filename=download_record.filename,
            media_type='application/octet-stream'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")


@router.get("/{file_id}/status", response_model=FileProcessingStatus)
async def get_file_status(
    file_id: str,
    db: Session = Depends(get_db)
):
    """
    获取文件处理状态
    
    查询文件的处理状态和进度
    """
    try:
        # 查询文件记录
        file_record = db.query(FileRecord).filter(
            FileRecord.file_id == file_id,
            FileRecord.is_deleted == False
        ).first()
        
        if not file_record:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 确定状态
        if file_record.is_processed:
            status = "completed"
            progress = 100.0
            message = "文件处理完成"
        else:
            status = "pending"
            progress = 0.0
            message = "文件等待处理"
        
        return FileProcessingStatus(
            file_id=file_record.file_id,
            status=status,
            progress=progress,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件状态失败: {str(e)}")
