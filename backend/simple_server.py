#!/usr/bin/env python3
"""
简化的后端服务器 - 用于阶段一测试
不依赖外部LLM API，使用模拟数据
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import time
import json

# 创建FastAPI应用
app = FastAPI(
    title="笔录转换系统",
    version="0.1.0-alpha",
    description="智能化笔录转换工具 - 阶段一测试版本"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class TransformRequest(BaseModel):
    text: str
    rules: Optional[list] = []
    llm_provider: Optional[str] = "mock"

class QualityMetrics(BaseModel):
    word_retention_rate: float
    quote_retention_rate: float
    content_preservation_rate: float

class TransformResponse(BaseModel):
    id: int
    original_text: str
    transformed_text: str
    status: str
    rules_applied: list
    llm_provider: str
    metrics: QualityMetrics
    error_message: Optional[str] = None
    created_at: str

class HealthCheck(BaseModel):
    status: str
    timestamp: str
    version: str
    database: str
    llm_service: str

# 模拟数据存储
transform_records = {}
record_counter = 0

def mock_transform_text(text: str) -> str:
    """模拟文本转换功能"""
    # 简单的模拟转换：将对话转换为第一人称叙述
    lines = text.split('\n')
    transformed_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 识别发言人模式
        if '：' in line:
            parts = line.split('：', 1)
            speaker = parts[0].strip()
            content = parts[1].strip()
            
            # 如果是访谈者(M)，跳过
            if speaker.upper() in ['M', 'MODERATOR', '访谈者', '主持人']:
                continue
            
            # 转换为第一人称
            if content:
                transformed_lines.append(content)
        else:
            transformed_lines.append(line)
    
    # 组织成段落
    result = "【访谈内容】\n" + "\n".join(transformed_lines)
    return result

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "笔录转换系统 API - 阶段一测试版",
        "version": "0.1.0-alpha",
        "docs": "/docs",
        "health": "/api/v1/health"
    }

@app.get("/api/v1/health", response_model=HealthCheck)
async def health_check():
    """健康检查"""
    return HealthCheck(
        status="healthy",
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
        version="0.1.0-alpha",
        database="mock",
        llm_service="mock"
    )

@app.post("/api/v1/transform", response_model=TransformResponse)
async def transform_text(request: TransformRequest):
    """文本转换"""
    global record_counter
    record_counter += 1
    
    # 模拟转换过程
    transformed_text = mock_transform_text(request.text)
    
    # 计算模拟质量指标
    original_len = len(request.text)
    transformed_len = len(transformed_text)
    
    metrics = QualityMetrics(
        word_retention_rate=min(transformed_len / original_len, 1.0) if original_len > 0 else 0.0,
        quote_retention_rate=0.75,  # 模拟值
        content_preservation_rate=0.85  # 模拟值
    )
    
    # 创建响应
    response = TransformResponse(
        id=record_counter,
        original_text=request.text,
        transformed_text=transformed_text,
        status="completed",
        rules_applied=request.rules or ["speaker_identification", "language_optimization"],
        llm_provider=request.llm_provider or "mock",
        metrics=metrics,
        created_at=time.strftime("%Y-%m-%d %H:%M:%S")
    )
    
    # 存储记录
    transform_records[record_counter] = response
    
    return response

@app.get("/api/v1/transform/{record_id}", response_model=TransformResponse)
async def get_transform_result(record_id: int):
    """获取转换结果"""
    if record_id not in transform_records:
        return {"error": "记录不存在"}, 404
    
    return transform_records[record_id]

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动笔录转换系统后端服务 (简化版)...")
    print("📍 服务地址: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    print("❤️  健康检查: http://localhost:8001/api/v1/health")
    print("🧪 这是阶段一测试版本，使用模拟转换功能")
    print()
    
    uvicorn.run(
        "simple_server:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )
