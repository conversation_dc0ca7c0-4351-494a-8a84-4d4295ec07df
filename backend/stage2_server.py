#!/usr/bin/env python3
"""
阶段二后端服务器 - 集成文件处理功能
包含文件上传、解析、转换和下载功能
"""
from fastapi import FastAPI, UploadFile, File, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any
import time
import json
import uuid
import os
import tempfile
import aiofiles
from pathlib import Path
from docx import Document

# 创建FastAPI应用
app = FastAPI(
    title="笔录转换系统",
    version="0.2.0-alpha",
    description="智能化笔录转换工具 - 阶段二：文件处理功能"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
UPLOAD_PATH = Path("./uploads")
UPLOAD_PATH.mkdir(exist_ok=True)
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {'txt', 'docx'}

# 数据模型
class TransformRequest(BaseModel):
    text: str
    rules: Optional[list] = []
    llm_provider: Optional[str] = "mock"

class QualityMetrics(BaseModel):
    word_retention_rate: float
    quote_retention_rate: float
    content_preservation_rate: float

class TransformResponse(BaseModel):
    id: int
    original_text: str
    transformed_text: str
    status: str
    rules_applied: list
    llm_provider: str
    metrics: QualityMetrics
    error_message: Optional[str] = None
    created_at: str

class FileUploadResponse(BaseModel):
    file_id: str
    original_filename: str
    file_size: int
    file_type: str
    character_count: int
    line_count: int
    text_preview: str
    upload_time: str

class FileContent(BaseModel):
    file_id: str
    original_filename: str
    text_content: str
    character_count: int
    line_count: int

class HealthCheck(BaseModel):
    status: str
    timestamp: str
    version: str
    database: str
    llm_service: str

# 模拟数据存储
transform_records = {}
file_records = {}
record_counter = 0

def mock_transform_text(text: str) -> str:
    """模拟文本转换功能"""
    lines = text.split('\n')
    transformed_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 识别发言人模式
        if '：' in line:
            parts = line.split('：', 1)
            speaker = parts[0].strip()
            content = parts[1].strip()
            
            # 如果是访谈者(M)，跳过
            if speaker.upper() in ['M', 'MODERATOR', '访谈者', '主持人']:
                continue
            
            # 转换为第一人称
            if content:
                transformed_lines.append(content)
        else:
            transformed_lines.append(line)
    
    # 组织成段落
    result = "【访谈内容】\n" + "\n".join(transformed_lines)
    return result

async def extract_text_from_file(file_path: Path, file_extension: str) -> str:
    """从文件中提取文本内容"""
    if file_extension == '.txt':
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return content
        except UnicodeDecodeError:
            # 尝试其他编码
            for encoding in ['gbk', 'gb2312', 'latin1']:
                try:
                    async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                        content = await f.read()
                        return content
                except UnicodeDecodeError:
                    continue
            raise ValueError("无法解码文件，请检查文件编码")
    
    elif file_extension == '.docx':
        try:
            doc = Document(file_path)
            paragraphs = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    paragraphs.append(paragraph.text)
            
            return '\n'.join(paragraphs)
        except Exception as e:
            raise ValueError(f"DOCX文件解析失败: {str(e)}")
    
    else:
        raise ValueError(f"不支持的文件类型: {file_extension}")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "笔录转换系统 API - 阶段二：文件处理功能",
        "version": "0.2.0-alpha",
        "docs": "/docs",
        "health": "/api/v1/health"
    }

@app.get("/api/v1/health", response_model=HealthCheck)
async def health_check():
    """健康检查"""
    return HealthCheck(
        status="healthy",
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
        version="0.2.0-alpha",
        database="mock",
        llm_service="mock"
    )

@app.post("/api/v1/transform", response_model=TransformResponse)
async def transform_text(request: TransformRequest):
    """文本转换"""
    global record_counter
    record_counter += 1
    
    # 模拟转换过程
    transformed_text = mock_transform_text(request.text)
    
    # 计算模拟质量指标
    original_len = len(request.text)
    transformed_len = len(transformed_text)
    
    metrics = QualityMetrics(
        word_retention_rate=min(transformed_len / original_len, 1.0) if original_len > 0 else 0.0,
        quote_retention_rate=0.75,  # 模拟值
        content_preservation_rate=0.85  # 模拟值
    )
    
    # 创建响应
    response = TransformResponse(
        id=record_counter,
        original_text=request.text,
        transformed_text=transformed_text,
        status="completed",
        rules_applied=request.rules or ["speaker_identification", "language_optimization"],
        llm_provider=request.llm_provider or "mock",
        metrics=metrics,
        created_at=time.strftime("%Y-%m-%d %H:%M:%S")
    )
    
    # 存储记录
    transform_records[record_counter] = response
    
    return response

@app.post("/api/v1/file/upload", response_model=FileUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """
    上传文件
    
    支持的文件格式：
    - .txt (文本文件)
    - .docx (Word文档)
    
    文件大小限制：10MB
    """
    try:
        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        file_extension = Path(file.filename).suffix.lower()
        if file_extension.lstrip('.') not in ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型。支持的格式: {', '.join(ALLOWED_EXTENSIONS)}"
            )
        
        # 读取文件内容
        content = await file.read()
        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413, 
                detail=f"文件大小超过限制 ({MAX_FILE_SIZE} bytes)"
            )
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        stored_filename = f"{file_id}{file_extension}"
        file_path = UPLOAD_PATH / stored_filename
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(content)
        
        # 提取文本内容
        text_content = await extract_text_from_file(file_path, file_extension)
        
        # 创建文件记录
        file_info = {
            "file_id": file_id,
            "original_filename": file.filename,
            "stored_filename": stored_filename,
            "file_size": len(content),
            "file_type": file_extension.lstrip('.'),
            "text_content": text_content,
            "character_count": len(text_content),
            "line_count": len(text_content.split('\n')),
            "upload_path": str(file_path),
            "upload_time": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        file_records[file_id] = file_info
        
        # 构建响应
        text_preview = text_content[:200] + "..." if len(text_content) > 200 else text_content
        
        return FileUploadResponse(
            file_id=file_id,
            original_filename=file.filename,
            file_size=len(content),
            file_type=file_extension.lstrip('.'),
            character_count=len(text_content),
            line_count=len(text_content.split('\n')),
            text_preview=text_preview,
            upload_time=file_info["upload_time"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.get("/api/v1/file/{file_id}", response_model=FileContent)
async def get_file_content(file_id: str):
    """获取文件内容"""
    if file_id not in file_records:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    file_info = file_records[file_id]
    
    return FileContent(
        file_id=file_id,
        original_filename=file_info["original_filename"],
        text_content=file_info["text_content"],
        character_count=file_info["character_count"],
        line_count=file_info["line_count"]
    )

@app.get("/api/v1/transform/{record_id}", response_model=TransformResponse)
async def get_transform_result(record_id: int):
    """获取转换结果"""
    if record_id not in transform_records:
        raise HTTPException(status_code=404, detail="转换记录不存在")
    
    return transform_records[record_id]

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动笔录转换系统后端服务 (阶段二)...")
    print("📍 服务地址: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    print("❤️  健康检查: http://localhost:8001/api/v1/health")
    print("📁 文件上传: http://localhost:8001/api/v1/file/upload")
    print("🎯 这是阶段二版本，支持文件上传和处理功能")
    print()
    
    uvicorn.run(
        "stage2_server:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )
