# 部署指南

## 🚀 部署架构概览

### 生产环境推荐架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx/CDN     │    │   前端 (Next.js) │    │   后端 (FastAPI) │
│   负载均衡/SSL   │────│   静态资源服务   │────│   API 服务       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                              │
                              ┌─────────────────┐    ┌─────────────────┐
                              │  PostgreSQL     │    │    Redis        │
                              │  主数据库       │    │   缓存/会话     │
                              └─────────────────┘    └─────────────────┘
                                              │
                              ┌─────────────────┐    ┌─────────────────┐
                              │   LLM 服务      │    │   监控告警      │
                              │ Ollama/API服务  │    │ Prometheus/等   │
                              └─────────────────┘    └─────────────────┘
```

## 🐳 Docker 部署

### Docker Compose 快速部署
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8000
      - NODE_ENV=production
    depends_on:
      - backend

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/transcribe_db
      - REDIS_URL=redis://redis:6379
      - LLM_PROVIDER=ollama
      - OLLAMA_BASE_URL=http://ollama:11434
    depends_on:
      - postgres
      - redis
      - ollama
    volumes:
      - ./backend/uploads:/app/uploads

  # 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=transcribe_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"

  # 缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # 本地 LLM 服务
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    command: serve

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend

volumes:
  postgres_data:
  redis_data:
  ollama_data:
```

### 前端 Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# 运行时镜像
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 后端 Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 开始部署笔录转换系统..."

# 检查必要的文件
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml 文件不存在"
    exit 1
fi

# 停止现有服务
echo "📦 停止现有服务..."
docker-compose down

# 拉取最新镜像并构建
echo "🔨 构建镜像..."
docker-compose build --no-cache

# 初始化 Ollama 模型
echo "🤖 初始化 LLM 模型..."
docker-compose up -d ollama
sleep 10
docker-compose exec ollama ollama pull qwen2.5:7b-instruct

# 启动所有服务
echo "🎯 启动所有服务..."
docker-compose up -d

# 等待服务就绪
echo "⏳ 等待服务启动..."
sleep 30

# 数据库迁移
echo "🗄️ 执行数据库迁移..."
docker-compose exec backend alembic upgrade head

# 健康检查
echo "🔍 健康检查..."
docker-compose exec backend python -c "
import requests
import sys
try:
    resp = requests.get('http://localhost:8000/health')
    if resp.status_code == 200:
        print('✅ 后端服务正常')
    else:
        print('❌ 后端服务异常')
        sys.exit(1)
except Exception as e:
    print(f'❌ 后端服务连接失败: {e}')
    sys.exit(1)
"

echo "🎉 部署完成！"
echo "📋 服务状态:"
docker-compose ps
echo ""
echo "🌐 访问地址:"
echo "  前端: http://localhost:3000"
echo "  后端API: http://localhost:8000"
echo "  API文档: http://localhost:8000/docs"
```

## ☁️ 云服务部署

### Vercel 前端部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录并部署
vercel login
vercel --prod

# 配置环境变量
vercel env add NEXT_PUBLIC_API_URL production
vercel env add NEXT_PUBLIC_APP_NAME production
```

```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "NEXT_PUBLIC_API_URL": "@api-url"
  }
}
```

### Railway 后端部署
```bash
# 安装 Railway CLI
npm install -g @railway/cli

# 登录并初始化
railway login
railway init

# 部署
railway up
```

```toml
# railway.toml
[build]
builder = "DOCKERFILE"
dockerfile = "Dockerfile"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[[services]]
name = "backend"
source = "."

[services.variables]
DATABASE_URL = "${{Postgres.DATABASE_URL}}"
REDIS_URL = "${{Redis.REDIS_URL}}"
```

### AWS ECS 部署
```yaml
# docker-compose.aws.yml
version: '3.8'

services:
  backend:
    image: your-ecr-registry/transcribe-backend:latest
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

  redis:
    image: redis:7-alpine
```

## 🔧 环境配置

### 生产环境变量配置

**前端环境变量 (.env.production)**:
```bash
# API 配置
NEXT_PUBLIC_API_URL=https://api.your-domain.com
NEXT_PUBLIC_APP_NAME=笔录转换系统
NEXT_PUBLIC_APP_VERSION=1.0.0

# 功能开关
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_SENTRY=true
NEXT_PUBLIC_ENABLE_AUTH=true

# 第三方服务
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
NEXT_PUBLIC_GA_TRACKING_ID=your-ga-id
```

**后端环境变量 (.env.production)**:
```bash
# 应用配置
APP_NAME=笔录转换系统
APP_VERSION=1.0.0
DEBUG=false
SECRET_KEY=your-super-secret-key

# 数据库配置
DATABASE_URL=************************************/dbname
REDIS_URL=redis://host:6379

# LLM 配置
LLM_PROVIDER=deepseek  # ollama, openai, deepseek
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_BASE_URL=https://api.deepseek.com
OLLAMA_BASE_URL=http://ollama:11434
OLLAMA_MODEL=qwen2.5:7b-instruct

# 文件存储
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=/app/uploads
ALLOWED_EXTENSIONS=txt,docx

# 安全配置
JWT_SECRET_KEY=your-jwt-secret
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
CORS_ORIGINS=https://your-frontend-domain.com

# 外部服务
SENTRY_DSN=your-sentry-dsn
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=your-email
SMTP_PASSWORD=your-password
```

### Nginx 配置
```nginx
# nginx/nginx.conf
upstream frontend {
    server frontend:3000;
}

upstream backend {
    server backend:8000;
}

# 主服务器配置
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

    # API 代理
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传大小限制
        client_max_body_size 10M;
    }

    # 前端代理
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控与日志

### 健康检查端点
```python
# backend/app/api/health.py
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.llm_service import LLMService

router = APIRouter()

@router.get("/health")
async def health_check(db: Session = Depends(get_db)):
    """系统健康检查"""
    checks = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.APP_VERSION,
        "checks": {}
    }
    
    # 数据库连接检查
    try:
        db.execute("SELECT 1")
        checks["checks"]["database"] = "healthy"
    except Exception as e:
        checks["checks"]["database"] = f"unhealthy: {str(e)}"
        checks["status"] = "unhealthy"
    
    # LLM 服务检查
    try:
        llm_service = LLMService()
        await llm_service.ping()
        checks["checks"]["llm"] = "healthy"
    except Exception as e:
        checks["checks"]["llm"] = f"unhealthy: {str(e)}"
        checks["status"] = "unhealthy"
    
    return checks
```

### Prometheus 监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'transcribe-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: /metrics
    scrape_interval: 5s

  - job_name: 'transcribe-frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: /api/metrics
    scrape_interval: 10s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
```

### 日志配置
```python
# backend/app/core/logging.py
import logging
import sys
from pathlib import Path

def setup_logging():
    """配置应用日志"""
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 日志格式
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # 文件处理器
    file_handler = logging.FileHandler(log_dir / "app.log")
    file_handler.setFormatter(formatter)
    
    # 错误日志处理器
    error_handler = logging.FileHandler(log_dir / "error.log")
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
```

## 🔐 安全配置

### SSL/TLS 证书申请
```bash
# 使用 Let's Encrypt 申请免费证书
sudo apt install certbot python3-certbot-nginx

# 申请证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 数据库安全配置
```sql
-- 创建数据库用户和权限
CREATE USER transcribe_user WITH ENCRYPTED PASSWORD 'strong_password';
CREATE DATABASE transcribe_db OWNER transcribe_user;
GRANT ALL PRIVILEGES ON DATABASE transcribe_db TO transcribe_user;

-- 设置连接限制
ALTER USER transcribe_user CONNECTION LIMIT 20;
```

### 防火墙配置
```bash
# Ubuntu/Debian 防火墙配置
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 5432/tcp  # 拒绝外部数据库访问
sudo ufw deny 6379/tcp  # 拒绝外部 Redis 访问
```

## 🚨 故障排除

### 常见问题诊断

**1. 后端服务无法启动**
```bash
# 检查日志
docker-compose logs backend

# 检查数据库连接
docker-compose exec backend python -c "
from app.core.database import engine
from sqlalchemy import text
with engine.connect() as conn:
    result = conn.execute(text('SELECT 1'))
    print('数据库连接正常')
"
```

**2. 前端无法连接后端**
```bash
# 检查网络连接
docker-compose exec frontend curl -f http://backend:8000/health

# 检查环境变量
docker-compose exec frontend env | grep NEXT_PUBLIC_API_URL
```

**3. LLM 服务异常**
```bash
# 检查 Ollama 状态
docker-compose exec ollama ollama list

# 重新拉取模型
docker-compose exec ollama ollama pull qwen2.5:7b-instruct
```

### 性能调优建议

**数据库优化**:
```sql
-- 添加必要索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_transforms_user_id ON transforms(user_id);
CREATE INDEX idx_transforms_created_at ON transforms(created_at);

-- 设置连接池
-- postgresql.conf
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
```

**Redis 配置优化**:
```bash
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

---

更多信息请参考：
- [技术架构](README_architecture.md)
- [用户指南](README_user_guide.md)
- [开发指南](README_development.md) 