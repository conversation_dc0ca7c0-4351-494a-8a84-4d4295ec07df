---
description: 
globs: **/*.css
alwaysApply: false
---
# Tailwind CSS Framework Rules (Tailwind CSS 框架规则)

## 1. 核心原则 (Core Principles)
- **功能类优先 (Utility-First)**: 始终优先使用 Tailwind CSS提供的功能类 (utility classes) 来构建用户界面，而不是编写自定义 CSS。
- **响应式设计 (Responsive Design)**: 利用 Tailwind 的响应式前缀 (如 `sm:`, `md:`, `lg:`) 来实现跨设备的自适应布局。

  ```html
  <div class="w-full md:w-1/2 lg:w-1/3">
    <!-- 移动设备上全宽，中等屏幕上占一半，大屏幕上占三分之一 -->
  </div>
  ```

- **状态变体 (State Variants)**: 使用状态变体 (如 `hover:`, `focus:`, `active:`, `disabled:`) 来处理不同交互状态下的样式。

  ```html
  <button class="bg-blue-500 hover:bg-blue-600 focus:ring-2">
    点击我
  </button>
  ```

## 2. 配置与定制 (Configuration & Customization)
- **`tailwind.config.js`**: 
  - 在此文件中进行项目特定的 Tailwind CSS 配置和定制。
  - **主题扩展 (Theme Extension)**: 当需要自定义颜色、字体、间距、断点等时，应通过 `theme.extend` 来扩展默认主题，而不是完全覆盖。
  - **插件 (Plugins)**: 根据需要添加 Tailwind CSS 插件以增强功能 (如 `@tailwindcss/forms`, `@tailwindcss/typography`)。
- **JIT 模式 (Just-In-Time Mode)**: 确保 JIT 模式已启用 (Tailwind CSS v3.x 默认启用)，以获得更快的编译速度和更小的生产构建体积。

## 3. 最佳实践 (Best Practices)
- **保持类名简洁可读**: 
  - 虽然功能类可能导致 HTML 模板中类名较多，但应努力保持其组织性和可读性。
  - 对于非常复杂或重复的类名组合，可以考虑提取为组件或使用 `@apply` (谨慎使用)。
- **`@apply` 的使用**: 
  - `@apply` 可以用于将一组 Tailwind 功能类提取到自定义 CSS 类中，但应谨慎使用，以避免回到传统 CSS 的编写方式，从而失去 Tailwind 的部分优势。
  - 优先用于小范围、可复用的样式片段，或在无法直接应用功能类的地方 (如伪元素内容)。

  ```css
  @layer components {
    .btn-primary {
      @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600;
    }
  }
  ```

- **避免过度定制**: 尽可能利用 Tailwind 的默认设计系统。只有在品牌和设计要求确实无法通过默认配置满足时才进行定制。
- **与组件库协同**: 
  - 当使用基于 Tailwind CSS 的组件库 (如 Shadcn UI 风格的组件) 时，遵循该组件库的样式和定制规范。
  - 自定义组件时，应基于 Tailwind CSS 构建，确保与项目整体风格一致 (参照 `README.md` 中的组件策略)。
- **暗黑模式 (Dark Mode)**: 如果项目需要支持暗黑模式，利用 Tailwind CSS 的 `dark:` 变体进行实现。
- **使用任意值**: 对于特定需求，可使用任意值语法：

  ```html
  <div class="top-[117px] grid-cols-[1fr_2fr]">
    <!-- 自定义定位和网格布局 -->
  </div>
  ```

- **使用间距工具实现一致布局**:

  ```html
  <div class="space-y-4">
    <div>项目 1</div>
    <div>项目 2</div>
  </div>
  ```

## 4. HTML 结构与语义化 (HTML Structure & Semantics)
- 即使使用功能类，也应保持 HTML 结构的语义化和清晰。Tailwind CSS 作用于样式层，不应替代良好的 HTML 结构。

## 5. 代码风格与格式化 (Code Style & Formatting)
- **类名排序 (Class Order)**: 考虑使用 Prettier 插件 (如 `prettier-plugin-tailwindcss`) 来自动排序 Tailwind CSS 类名，以提高一致性和可读性。

## 6. 与框架集成 (Framework Integration)
- **Next.js/React**: 
  - 确保 Tailwind CSS 正确配置在 Next.js 项目中 (通常在 `tailwind.config.js` 和 `postcss.config.js` 中，并通过在全局 CSS 文件中导入 Tailwind 指令)。
  - (其他框架的集成细节可按需补充)

## 7. 避免的问题 (Pitfalls to Avoid)
- **过度使用 `@apply`**: 可能导致失去 Tailwind 的维护优势和 JIT 模式的性能优化。
- **创建不必要的自定义CSS**: 优先通过 Tailwind 的功能类和配置解决样式需求。
- **忽略响应式设计和状态变体**: 未充分利用 Tailwind 的内置功能来实现动态和交互式 UI。



