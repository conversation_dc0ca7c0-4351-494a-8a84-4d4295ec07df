---
description: 当 Agent 调用 LLM 时
globs: 
alwaysApply: false
---
# LLM Interaction Rules (LLM 交互规范)

## 1. Prompt Engineering (提示工程)
- **清晰具体**: 向 LLM 提供的提示应尽可能清晰、明确、具体，避免歧义。
- **上下文充足**: 提供足够的上下文信息，帮助 LLM 理解任务背景和约束条件。例如，处理哪部分文本，期望的输出格式，被访者的角色等。
- **角色设定**: 在提示中可以为 LLM设定一个角色 (e.g., "你是一个专业的笔录整理员")，以引导其输出风格和重点。
- **指定输出格式**: 如果需要特定格式的输出（如 JSON、Markdown、纯文本分段），在提示中明确指出。
- **迭代优化**: Prompt Engineering 是一个迭代过程。根据 LLM 的输出效果不断调整和优化提示词。
- **少量样本学习 (Few-shot Learning)**: 对于复杂任务，可以在提示中提供几个输入输出的示例，帮助 LLM 理解模式。

## 2. 核心任务处理 (Core Task Handling)
- **视角转换**: 提示应明确指出以哪位发言人为第一人称进行转换。
- **语气词/口头禅处理**: 
    - 定义项目中需要处理的常见语气词和口头禅列表（可持续补充）。
    - 在提示中告知 LLM 如何处理这些词语（例如，删除、替换为更书面的表达等）。
    - 注意保留必要的、体现被访者个性的口语表达，避免过度"净化"。
- **发言人识别**: (虽然 MVP 阶段初步基于模式匹配，但未来 LLM 可辅助)
    - 如果使用 LLM 辅助识别，需设计能准确从对话中提取发言人标记的提示。

## 3. 结果验证与人工干预 (Result Verification & Human Intervention)
- **人工审查**: LLM 生成的转换结果必须经过人工审查和校对，确保准确性、流畅性和符合口述习惯。
- **可配置性**: 对于一些处理规则（如特定口头禅的保留与否），未来可以考虑提供配置选项，而不是完全依赖 LLM 的通用判断。

## 4. 性能与成本考量 (Performance & Cost Consideration) (若使用云端LLM API)
- **提示长度**: 注意提示词的长度，过长的提示可能增加处理时间和成本。
- **API 调用频率**: 优化前端与后端 LLM 服务的交互，避免不必要的 API 调用。

## 5. 安全与偏见 (Security & Bias)
- 警惕 LLM 可能产生的偏见性输出，并进行纠正。
- 避免向 LLM 输入敏感信息（虽然本项目主要处理用户上传的笔录，也需注意）。



