---
description: 
globs: **/*.md
alwaysApply: false
---
# Documentation Rules (文档编写规范)

## 1. README.md
- `README.md` 是项目的入口，应包含以下关键信息：
  - **项目简介**: 清晰描述项目的目标和核心功能。
  - **功能模块与设计**: (特别是MVP阶段) 详细描述主要功能模块和核心设计思路。
  - **技术栈**: 列出主要使用的语言、框架和工具。
  - **开发计划**: (特别是MVP阶段) 主要开发阶段和任务拆解。
  - **部署方案**: (待讨论后补充) 项目如何部署。
- `README.md` 应保持更新，以反映项目的最新状态。以下情况应触发 `README.md` 的更新：
  - **技术栈变更**: 新增、删除或更改主要的技术、框架或库时。
  - **核心功能变更**: 项目的核心功能发生重大添加、修改或移除时。
  - **重要设计决策变更**: 影响项目整体架构或关键模块的重大设计决策发生变化时。
  - **开发计划调整或任务状态更新**: 开发计划、里程碑或阶段性目标有显著调整时，或开发计划中的任务被完成时，应及时在 `README.md` (或其引用的文档) 中标明最新状态。
  - **部署方案确定或变更**: 项目的部署方案被确定或发生重要变化时。
  - **定期审查**: 即使没有明确的变更事件，也应定期（如每个迭代结束或版本发布前）审查 `README.md` 的准确性和完整性。

## 2. 代码注释 (Code Comments)
- **为什么，而不是做什么**: 注释应解释代码背后的逻辑、意图或复杂决策，而不是简单地复述代码本身的功能（代码本身应该自解释其功能）。
- **公共 API 注释**: 对于对外暴露的函数、类、方法和模块，应有清晰的文档字符串 (docstrings)，说明其用途、参数、返回值和可能的异常。
  - Python: 遵循 PEP 257 (Docstring Conventions)。
  - TypeScript: 使用 TSDoc 或 JSDoc 风格的注释。
- **复杂逻辑注释**: 对于算法复杂、逻辑不易理解的代码段，应添加注释解释。
- **TODO/FIXME**: 
    - 使用 `TODO:` 标记待完成的功能或改进点。
    - 使用 `FIXME:` 标记已知问题或需要修复的 bug，并简要说明。
- 保持注释与代码同步更新。

## 3. API 文档 (API Documentation) (后端 FastAPI)
- FastAPI 自动生成的交互式 API 文档 (Swagger UI / ReDoc) 是主要的 API 文档来源。
- 在 FastAPI 的路径操作函数 (path operation functions) 中使用清晰的描述、参数说明、响应模型，以确保自动生成的文档质量高且易于理解。
- 示例和使用说明应清晰。

## 4. 配置文件说明 (Configuration Files)
- 如果项目包含复杂的配置文件，应提供文档说明各配置项的含义和作用。

## 5. 版本记录规范 (Version History Specification)
- **CHANGELOG**: 使用根目录下的 `CHANGELOG.md` 文件记录所有版本的变更历史。
- **语义化版本 (Semantic Versioning)**: 
  - 严格遵循 [语义化版本 2.0.0](mdc:https:/semver.org/lang/zh-CN) 规范 (MAJOR.MINOR.PATCH)。
  - `MAJOR` 版本号：当进行不兼容的 API 修改时。
  - `MINOR` 版本号：当以向后兼容的方式添加功能时。
  - `PATCH` 版本号：当进行向后兼容的 bug 修复时。
- **版本条目内容**: 每个版本的记录应清晰地分为以下几个部分（如果适用）：
  - `Added` (新增功能)
  - `Changed` (功能变更)
  - `Deprecated` (不再建议使用的功能)
  - `Removed` (移除的功能)
  - `Fixed` (问题修复)
  - `Security` (安全相关的修复)
- **格式建议**: 可以参考 [Keep a Changelog](mdc:https:/keepachangelog.com/zh-CN/1.0.0) 的格式建议。




