---
description: 当涉及敏感数据相关开发时
globs: 
alwaysApply: false
---
# 敏感数据保护规范 (Sensitive Data Protection)

## 1. 目标与重要性
- **目标**: 保护项目处理、存储和传输的所有敏感数据，防止未经授权的访问、泄露、修改或丢失。
- **重要性**: 敏感数据泄露可能导致用户隐私侵犯、法律责任、声誉损害和经济损失。

## 2. 识别敏感数据
- **定义**: 敏感数据是指一旦泄露或被滥用，可能对个人或组织造成伤害的信息。
- **本项目中的潜在敏感数据 (`README.md` 分析)**:
    - **用户上传的笔录内容**: 这是核心的用户数据。虽然 MVP 阶段可能不涉及典型的 PII (个人身份信息)如身份证号、银行账户，但访谈内容本身可能包含个人观点、未公开信息等，应视为敏感。
    - **LLM API 密钥**: 如果使用云端 LLM 服务，API 密钥是高度敏感的凭证。
    - **服务器配置**: 某些服务器配置信息（如特定路径、内部服务地址）如果泄露也可能带来风险。
- **通用敏感数据示例**: 密码、API 密钥、访问令牌、个人身份信息 (PII)、健康记录、财务信息、商业秘密等。

## 3. 数据处理原则
- **数据最小化 (Data Minimization)**: 
    - 只收集、处理和存储绝对必要的敏感数据。
    - 不要在不需要的地方传递或复制敏感数据。
- **目的限制 (Purpose Limitation)**: 敏感数据只能用于收集时声明的特定、明确且合法的目的。
- **存储限制 (Storage Limitation)**: 敏感数据不应存储超过其所需的最短时间。一旦不再需要，应安全删除或匿名化。
    - (本项目 MVP 不涉及数据库持久化存储，但如果后续引入，此条非常重要)。

## 4. 安全存储
- **避免硬编码**: 
    - **严禁将 API 密钥、密码、数据库连接字符串等敏感凭证直接硬编码在源代码中。**
    - `README.md` 后端技术栈使用 Python/FastAPI，LLM 集成配置中明确指出"LLM 模型路径、API 密钥等敏感信息应通过环境变量或配置文件管理，不应硬编码。"
- **使用环境变量**: 
    - 将敏感配置存储在环境变量中是最常见的做法。
    - Python: 使用 `os.getenv()` 读取。
    - Node.js: 使用 `process.env`。
    - 确保 `.env` 文件被添加到 `.gitignore` 中，不提交到版本控制库。
- **配置文件管理**: 
    - 如果使用配置文件存储敏感信息，确保该文件本身受到严格的访问控制，并且不应包含在版本控制中（或使用模板文件和本地覆盖文件的方式）。
    - `config.py` 或类似文件应从环境变量加载敏感值。
- **加密存储 (At Rest)**: 如果需要持久化存储高度敏感数据 (如数据库中的 PII)，应对其进行加密。
    - (本项目 MVP 无数据库，此条为未来考虑)。

## 5. 安全传输 (In Transit)
- **使用 HTTPS/TLS**: 
    - 前端与后端 API 之间、后端与外部 LLM 服务之间的所有网络通信都必须使用 HTTPS/TLS 加密。
    - FastAPI 默认运行在 HTTP，生产部署时必须配置在 ASGI 服务器 (如 Uvicorn) 之后，并通过反向代理 (如 Nginx, Traefik) 提供 HTTPS。
- **内部网络**: 即使是内部服务之间的通信，如果涉及敏感数据，也应考虑加密。

## 6. 访问控制
- **最小权限原则**: 只有授权的人员和系统组件才能访问敏感数据，且权限应限制在完成其任务所必需的范围内。
- **日志与监控**: 记录对敏感数据的访问和操作，以便审计和检测异常行为。

## 7. 日志记录注意事项
- **避免在日志中记录敏感数据**: 
    - 审查日志配置和代码，确保密码、API 密钥、完整的用户笔录内容等不会被意外记录到日志文件中。
    - 如果需要记录与数据相关的元信息，考虑进行脱敏处理。
    - FastAPI 可以通过配置日志记录器和格式化器来控制日志内容。

## 8. 数据销毁
- 当敏感数据不再需要时，应通过安全的方式彻底销毁，而不仅仅是简单删除。
    - (对于内存中的数据，确保其在不再使用后被垃圾回收；对于文件，考虑安全删除工具)。

## 9. 遵循 `6.security/general.mdc` 中的通用安全规范。



