---
description: 当涉及LLM相关开发时
globs: 
alwaysApply: false
---
# LLM 安全规范 (LLM Security)

## 1. 目标与重要性
- **目标**: 识别和缓解在应用程序中集成和使用大型语言模型 (LLM) 时可能出现的特定安全风险。
- **重要性**: LLM 的引入带来了新的攻击向量和安全挑战，如 Prompt 注入、数据泄露、模型偏见等。

## 2. 主要风险与缓解措施

### a. Prompt 注入 (Prompt Injection)
- **风险**: 攻击者通过构造恶意输入 (用户提供的文本或上传的文件内容)，覆盖或操纵原始设计的 Prompt，从而使 LLM 执行非预期的操作、泄露敏感信息或生成有害内容。
- **缓解措施**:
    - **输入清理与验证**: 
        - 对所有输入到 Prompt 模板的用户内容进行严格的清理和验证。移除或转义潜在的指令性字符或标记 (例如，如果 LLM 使用特定标记来区分指令和数据)。
        - 限制用户输入的长度和格式。
    - **参数化 Prompts / 模板分离**: 
        - 将用户输入严格作为数据参数传递给 Prompt 模板，而不是直接将其拼接到指令部分。
        - 使用清晰的界定符或结构化格式 (如 JSON) 来区分指令和用户数据。
        - 示例 (概念性):
          ```python
          # 不安全的方式 (直接拼接)
          # prompt = f"将以下文本从英文翻译成中文：{user_input}"

          # 更安全的方式 (模板化)
          prompt_template = "任务：将文本从英文翻译成中文。\n文本：{data}"
          prompt = prompt_template.format(data=user_input)
          ```
    - **指令调整 (Instruction Tuning)**: 训练或微调 LLM 以更好地区分指令和数据，并抵抗操纵。
    - **输出过滤**: 对 LLM 的输出进行检查，看是否包含可疑的指令执行迹象或不应出现的内容。
    - **最小权限原则**: LLM 本身或其调用的下游工具不应被授予超出其任务范围的权限。
    - **人工监控与审计**: 对于敏感操作，考虑引入人工审核环节。

### b. 数据泄露 (Data Exfiltration / Privacy)
- **风险**: 
    - LLM 在其响应中意外泄露训练数据中的敏感信息。
    - 用户输入 (如上传的笔录) 可能包含敏感信息，这些信息在传递给 LLM (特别是云端 LLM API) 或在日志中记录时可能面临泄露风险。
- **缓解措施**:
    - **输入/输出数据最小化**: 只向 LLM 发送完成任务所必需的最少量信息。对 LLM 的响应也进行审查，去除不必要的敏感内容。
    - **数据匿名化/假名化**: 在将用户数据发送给 LLM 前，对敏感部分进行匿名化或假名化处理（如果可行且不影响任务质量）。
    - **本地化部署**: 如果可能且对数据隐私要求极高，优先考虑在本地或可信环境中部署 LLM 模型，而不是使用第三方云服务。(`README.md` 提到本地 LLM 模型是一个选项)
    - **API 密钥管理**: 安全存储和使用 LLM API 密钥，避免硬编码。使用环境变量或安全的密钥管理服务。**(参考 `6.security/sensitive_data.mdc`)**
    - **日志审查**: 仔细审查记录 LLM 输入输出的日志，确保不记录过多敏感信息，并对日志访问进行控制。
    - **用户告知与同意**: 如果使用云端 LLM，应明确告知用户其数据可能被如何处理，并获取同意。

### c. 有害内容生成 (Harmful Content Generation)
- **风险**: LLM 可能生成不准确、有偏见、冒犯性、歧视性或非法的内内容。
- **缓解措施**:
    - **内容过滤**: 对 LLM 的输出实施内容过滤机制，检测并移除或标记有害内容。
    - **Prompt 设计**: 精心设计 Prompt，引导 LLM 生成期望的、安全的内容。
    - **模型选择与微调**: 选择经过安全对齐或专门针对减少有害内容生成的模型。可以对模型进行微调以符合应用的安全标准。
    - **用户反馈机制**: 提供用户反馈渠道，以便报告和处理有害内容。

### d. 模型窃取与拒绝服务 (Model Theft & Denial of Service)
- **风险**: 
    - 恶意用户可能试图通过大量查询来推断模型架构或窃取模型权重 (如果模型是私有的)。
    - 大量无效或计算密集型的请求可能导致 LLM 服务过载，造成拒绝服务。
- **缓解措施**:
    - **访问控制与速率限制**: 对 LLM 服务的 API 端点实施严格的访问控制和速率限制。
    - **输入复杂度限制**: 限制输入文本的长度和复杂度，防止计算量过大的请求。
    - **监控与警报**: 监控 LLM 服务的使用情况，设置异常行为警报。

## 3. 本项目特定考量 (`README.md`)
- **发言人识别与视角转换**: Prompt 注入可能导致错误的发言人被选定或视角转换逻辑被篡改。
- **语气词/口头禅处理**: 恶意输入可能试图让 LLM 过度删除或保留特定词语，影响输出质量。
- **上传的 `.txt` 文件内容**: 这是主要的外部输入，需要重点防范 Prompt 注入。在将其内容填入 Prompt 模板前必须谨慎处理。

## 4. 通用安全实践
- **及时更新**: 保持 LLM 库 (如 `transformers`) 和模型到最新版本，以获取安全补丁。
- **代码审计**: 对与 LLM 交互的代码进行安全审计。

## 5. 遵循 `6.security/general.mdc` 中的通用安全规范。



