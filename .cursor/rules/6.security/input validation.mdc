---
description: 当涉及输入验证开发时
globs: 
alwaysApply: false
---
# 输入验证规范 (Input Validation)

## 1. 目标与重要性
- **目标**: 确保所有外部输入（来自用户、API客户端、第三方服务等）在被处理之前都是有效、安全和符合预期的。
- **重要性**: 输入验证是抵御多种常见 Web 攻击（如 XSS、SQL注入、命令注入、路径遍历、拒绝服务等）的第一道防线。

## 2. 核心原则: "永不信任输入"
- 始终假设所有外部输入都可能是恶意的或格式错误的。
- 在数据被应用程序的任何部分使用之前，必须对其进行验证。

## 3. 在哪里进行输入验证
- **服务端验证是强制性的**: 
    - 客户端验证（如 JavaScript 在浏览器中的验证）可以改善用户体验，但不应作为唯一的安全措施，因为客户端验证可以被轻易绕过。
    - **FastAPI**: 利用 Pydantic 模型进行请求体和查询参数的自动数据校验和类型转换。这是 FastAPI 的核心优势之一。
- **客户端验证作为辅助**: 可以提供即时反馈，减少不必要的后端请求。
- **边界验证**: 在数据跨越信任边界时（例如，从外部 API 获取数据后在内部使用前）进行验证。

## 4. 验证内容与方法
- **类型检查**: 验证数据是否符合预期的类型（如字符串、数字、布尔、日期、列表等）。Pydantic 会自动处理大部分类型检查。
- **格式验证**: 验证数据是否符合特定的格式要求（如电子邮件地址、URL、UUID、电话号码）。
    - Pydantic 提供了 `EmailStr`, `AnyHttpUrl` 等类型，以及可以通过 `constr` 和 `Field` 进行正则表达式约束。
- **长度/大小限制**: 对字符串、列表、文件大小等设置合理的最小和最大限制，以防止缓冲区溢出或拒绝服务攻击。
    - FastAPI/Pydantic: 使用 `min_length`, `max_length` (用于字符串和列表), `gt`, `ge`, `lt`, `le` (用于数字)。对于文件上传，FastAPI 允许通过 `UploadFile` 的 `size` 属性进行检查，并可以在服务器配置中限制最大请求体大小。
- **范围检查**: 验证数值是否在允许的范围内。
- **白名单验证 (Allow-list Validation)**: 
    - 对于只能接受一组特定值的输入（如枚举、特定命令），优先使用白名单验证，只允许已知的良好值。
    - 避免使用黑名单验证（拒绝已知的恶意值），因为黑名单很难维护且容易被绕过。
    - FastAPI/Pydantic: 使用 `Enum` 类型或 `Literal` 类型。
- **字符集限制**: 如果输入只应包含特定字符（如字母数字），则进行检查。
- **业务规则验证**: 验证数据是否符合应用程序的特定业务逻辑（例如，开始日期必须早于结束日期）。这通常在 Pydantic 模型验证之后，在服务层进行。

## 5. FastAPI/Pydantic 中的实践
- **使用 Pydantic 模型**: 
    - 为所有 API 请求体、查询参数、路径参数定义 Pydantic 模型。
    - 利用 Pydantic 的内置验证器和类型注解 (`str`, `int`, `bool`, `List`, `Optional`, `Union`, `EmailStr`, `HttpUrl` 等)。
    - 使用 `Field` 函数进行更细致的约束，如 `min_length`, `max_length`, `regex`, `gt`, `le` 等。
    - FastAPI 会自动处理验证，并在验证失败时返回标准的 HTTP 422 Unprocessable Entity 错误响应，包含详细的错误信息。
- **自定义验证器**: 
    - 对于复杂的验证逻辑，可以在 Pydantic 模型中使用 `@validator` 或 `@root_validator` 装饰器创建自定义验证函数。
- **处理文件上传**: 
    - `README.md` 中提到用户上传 `.txt` 文件。应验证文件类型 (MIME type，尽管 `.txt` 可能不明确，但可以检查扩展名和内容初步判断) 和文件大小。
    - FastAPI 的 `UploadFile` 对象提供了文件名 (`filename`) 和文件对象 (`file`)。

## 6. 安全编码实践
- **参数化查询**: 在与数据库交互时，始终使用参数化查询或 ORM 来防止 SQL 注入，而不是手动拼接 SQL 字符串。 (本项目 MVP 无数据库，但此为通用规则)
- **避免直接使用用户输入构造系统命令或文件路径**: 如果必须这样做，务必进行严格的清理和转义，以防止命令注入或路径遍历攻击。

## 7. 错误处理
- 当输入验证失败时，向客户端返回清晰、具体的错误信息，指出哪个字段或值不符合要求，但不应泄露过多的内部实现细节。
- FastAPI 的默认 422 响应通常能很好地处理这一点。

## 8. 遵循 `6.security/general.mdc` 中的通用安全规范。



