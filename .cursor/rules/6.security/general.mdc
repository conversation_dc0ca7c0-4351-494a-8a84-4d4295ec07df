---
description: 当涉及安全相关开发时
globs: 
alwaysApply: false
---
# 通用安全规范 (General Security)

## 1. 安全是每个人的责任
- **意识培养**: 团队所有成员都应具备基本的安全意识，理解常见的安全威胁和防护措施。
- **持续学习**: 安全领域不断发展，鼓励团队成员学习新的安全知识和技术。

## 2. 设计时考虑安全 (Security by Design)
- **早期介入**: 在需求分析和系统设计阶段就应考虑安全需求，而不是在开发后期才补充安全措施。
- **威胁建模**: 对于关键功能或处理敏感数据的模块，进行威胁建模，识别潜在的攻击向量和脆弱点。
- **最小权限原则 (Principle of Least Privilege)**: 
    - 用户、进程或组件只应被授予执行其预定功能所必需的最小权限。
    - API 端点也应遵循此原则，确保客户端只能访问其被授权的资源和操作。

## 3. 纵深防御 (Defense in Depth)
- 不依赖单一的安全措施，而是构建多层次的防护体系。
- 例如：输入验证 + 参数化查询 + Web 应用防火墙 (WAF) + 身份认证与授权。

## 4. 输入验证与输出编码
- **输入验证**: 
    - **永不信任用户输入**: 对所有来自外部（用户、第三方服务等）的数据进行严格的验证、清理 (sanitization) 和类型检查。
    - **参考 `6.security/input_validation.mdc` 规则。**
- **输出编码 (Output Encoding)**: 
    - 在将数据显示到不同上下文（如 HTML 页面、JavaScript、SQL 查询）时，进行适当的编码以防止注入攻击 (如 XSS, SQL注入)。
    - 例如，在 HTML 中显示用户数据时使用 HTML 实体编码。

## 5. 身份认证与授权 (Authentication & Authorization)
- **强身份认证**: 
    - 如果项目涉及用户账户，使用强密码策略，考虑多因素认证 (MFA)。
    - MVP 阶段本项目不涉及用户账户，但此为通用准则。
- **会话管理**: 安全地管理用户会话，防止会话劫持。
- **细粒度授权**: 确保用户只能访问其被授权的资源和功能。

## 6. 敏感数据保护
- **参考 `6.security/sensitive_data.mdc` 规则。**
- MVP 阶段主要涉及用户上传的笔录文本，虽然可能不包含密码等高敏感信息，但也应视为用户数据并妥善处理。

## 7. 依赖安全
- **参考 `6.security/Dependency.mdc` 规则。**

## 8. LLM 相关安全
- **参考 `6.security/llm_security.mdc` 规则。** (因为本项目核心功能依赖 LLM)

## 9. 错误处理与日志记录
- **避免泄露敏感信息**: 错误信息不应向最终用户泄露过多的系统内部细节或敏感数据 (如堆栈跟踪、数据库错误详情)。向用户显示通用的错误提示，并将详细错误记录到日志中。
- **安全日志**: 记录关键的安全事件 (如登录尝试、授权失败、重要操作)，以便审计和事后分析。

## 10. 安全测试
- **集成安全测试**: 将安全测试（如 SAST - 静态分析, DAST - 动态分析, 依赖扫描）集成到开发流程和 CI/CD 流水线中。
- **渗透测试**: 对于重要的应用，定期进行人工渗透测试。

## 11. 安全配置
- **默认安全**: 应用程序和基础设施的默认配置应该是安全的。
- **移除不必要的功能**: 关闭或移除不使用的服务、端口、特性，以减少攻击面。

## 12. 定期审查与更新
- 定期审查代码、配置和安全策略，以应对新的威胁和最佳实践的变化。



