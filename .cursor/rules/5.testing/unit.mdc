---
description: 当执行测试任务时
globs: 
alwaysApply: false
---
# 单元测试规范 (Unit Testing)

## 1. 目标与重要性
- **目标**: 验证应用程序中最小的可测试单元（如函数、方法、类或独立模块）的行为是否符合预期。
- **重要性**:
    - **快速反馈**: 单元测试运行速度快，能迅速定位问题到具体的代码单元。
    - **促进模块化设计**: 易于单元测试的代码通常具有良好的模块化和低耦合性。
    - **安全重构**: 有了全面的单元测试，开发者可以更有信心地重构代码，因为测试可以验证变更是否破坏了现有功能。
    - **"活文档"**: 单元测试描述了各个代码单元的预期行为。

## 2. 单元测试的特点
- **隔离性**: 单元测试应专注于被测试单元本身，其依赖项（如其他类、外部服务、数据库、文件系统）应被模拟 (mock) 或打桩 (stub)。
- **速度快**: 大量的单元测试应该能在几秒或几分钟内完成。
- **覆盖率高**: 单元测试应力求覆盖代码库的大部分逻辑分支和边界条件。

## 3. 测试框架与工具
- **Python (Backend)**:
    - `pytest`: 推荐使用，功能强大且易于使用。
    - `unittest` (内置库): Python 标准库的一部分，也可使用。
    - **Mocking**: `unittest.mock` (内置) 或 `pytest-mock` (pytest 插件)。
- **JavaScript/TypeScript (Frontend)**:
    - `Jest`: 流行的测试框架，功能全面，尤其适用于 React 项目。
    - `Vitest`: 一个非常快速的测试框架，与 Vite 构建工具集成良好。
    - `React Testing Library` (RTL): 推荐用于测试 React 组件，鼓励测试组件的行为而非实现细节。
    - **Mocking**: Jest 和 Vitest 都内置了强大的 mock 功能。

## 4. 单元测试用例设计
- **测试公共接口**: 应该测试单元的公共 API 和行为，而不是其内部实现细节。这使得测试在代码重构时更加健壮。
- **覆盖各种场景**:
    - **正常路径**: 测试单元在典型输入下的预期输出。
    - **边界条件**: 测试输入数据的边缘情况 (例如，空值、零值、最大/最小值、空列表/字符串)。
    - **错误/异常情况**: 测试当接收到无效输入或发生内部错误时，单元是否能正确抛出异常或返回错误状态。
- **每个测试一个关注点**: 一个测试用例通常只验证一个特定的行为或条件。
- **使用清晰的断言**: 断言应明确指出期望值和实际值，以便在测试失败时快速理解问题所在。

## 5. 模拟 (Mocking) 与打桩 (Stubbing)
- **目的**: 隔离被测试单元，消除对外部依赖的依赖，使测试更快速、更可靠。
- **Mock**: 用于验证被测试单元是否以预期的方式调用了其依赖项 (例如，某个方法是否被调用，以及调用时的参数)。
- **Stub**: 用于向被测试单元提供其依赖项的预设响应 (例如，一个函数调用总是返回一个特定的值)。
- **谨慎使用**: 过度使用 mock 可能导致测试与代码实现耦合过紧。应主要 mock 那些与被测试单元直接交互的依赖。

## 6. 前端组件单元测试 (使用 React Testing Library)
- **用户视角**: 测试组件的方式应尽可能接近用户实际使用该组件的方式。
- **查询元素**: 使用 RTL 提供的查询函数 (如 `getByRole`, `getByLabelText`, `getByText`) 来查找元素，而不是依赖 CSS 选择器或 `data-testid` (除非必要)。
- **交互与断言**: 模拟用户交互 (如 `fireEvent` 或 `@testing-library/user-event`)，然后断言组件的渲染结果或状态变化是否符合预期。
- **避免测试实现细节**: 不要测试组件的内部状态或生命周期方法。关注组件的输入 (props) 和输出 (渲染的 DOM, 回调函数的调用)。

## 7. 任务参考 (来自 README.md)
- **任务 1.2：发言人识别逻辑**: 这个纯逻辑模块非常适合进行单元测试，验证其能否正确从各种文本中提取发言人。
- **任务 1.3 (核心函数)**: LLM 调用的核心函数，在模拟 LLM 客户端后，可以进行单元测试以验证其参数处理和基本逻辑。
- 前端各种工具函数、自定义 Hooks 以及独立的 UI 组件的内部逻辑也是单元测试的良好候选。

## 8. 遵循 `5.testing/general.mdc` 中的通用测试规范。



