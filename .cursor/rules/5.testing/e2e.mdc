---
description: 当执行测试任务时
globs: 
alwaysApply: false
---
# 端到端测试规范 (E2E Testing)

## 1. 目标与重要性
- **目标**: 模拟真实用户场景，从用户界面 (UI) 到后端服务，验证整个应用程序的集成流程和功能是否按预期工作。
- **重要性**:
    - 发现跨多个组件或服务的集成问题。
    - 确保关键用户路径的正确性。
    - 提供对应用程序整体健康状况的信心。

## 2. 测试框架与工具
- **推荐工具**: 
    - **Playwright**: 由 Microsoft 开发，支持多种浏览器，API 强大且易于使用。
    - **Cypress**: 另一个流行的 E2E 测试框架，以其开发者体验和调试功能著称。
- **选择考量**: 根据团队熟悉度、项目需求和生态系统选择合适的工具。

## 3. 测试场景选择
- **关键用户流程**: 优先测试核心的用户场景和业务流程，例如：
    - 用户上传文件 -> 配置规则 -> 应用规则 -> 查看结果 -> 下载结果 (对应本项目 MVP 核心流程)。
    - 用户注册、登录、关键操作等 (如果项目包含这些功能)。
- **高风险区域**: 测试那些容易出错或变更频繁的功能模块。
- **避免冗余**: E2E 测试运行成本较高，应避免与单元测试或集成测试覆盖相同的逻辑细节。专注于验证流程的完整性。

## 4. 测试用例设计
- **用户视角**: 从用户的角度编写测试用例，关注用户可以执行的操作和期望看到的结果。
- **清晰的步骤**: 测试步骤应清晰、简洁，易于理解和维护。
- **独立的测试**: 每个 E2E 测试用例应尽可能独立，避免测试之间的依赖，以便于并行执行和定位失败。
- **数据管理**: 
    - 需要预置测试数据时，应有机制创建或重置数据，确保测试环境的一致性。
    - 避免测试用例之间共享可变数据导致相互影响。
- **等待策略**: 
    - E2E 测试需要处理异步操作和页面加载。使用测试框架提供的智能等待机制 (如 Playwright 的自动等待、Cypress 的 `cy.wait()`)，避免使用固定的 `sleep` 或 `timeout`。
    - 等待特定的元素出现、可见、可交互，或等待网络请求完成。

## 5. 选择器策略 (Locators)
- **健壮的选择器**: 使用稳定且不易因 UI 变更而失效的选择器。
    - 优先顺序: 
        1.  **面向用户的属性**: 如 ARIA roles, `alt` 文本, `placeholder`。
        2.  **测试专用的 ID**: `data-testid`, `data-cy` 等。
        3.  **CSS 选择器**: 尽量简洁，避免复杂的层级依赖。
        4.  **XPath**: 作为最后手段，因为其可能较为脆弱。
- **避免依赖文本内容**: 如果文本内容经常变化，不要将其作为主要的选择器依据 (除非是测试该文本本身)。

## 6. 测试环境
- **隔离的环境**: E2E 测试应在稳定且隔离的测试环境中运行，该环境应尽可能模拟生产环境。
- **后端依赖**: 确保 E2E 测试所依赖的后端服务是可用的，并且数据是一致的。

## 7. 运行与维护
- **CI/CD 集成**: 将 E2E 测试集成到 CI/CD 流水线中，但可以根据其运行时间考虑触发频率 (例如，不在每次提交时都运行，而是在合并到主分支前或夜间构建时运行)。
- **失败分析**: E2E 测试失败时，应提供足够的调试信息 (如截图、视频录制、控制台日志)，以便快速定位问题。
- **定期审查与重构**: 随着应用的迭代，E2E 测试用例也需要定期审查和重构，以保持其相关性和稳定性。

## 8. 任务 4.3 参考 (来自 README.md)
- 完整测试从文件上传到下载转换后笔录的整个 MVP 流程，确保各功能模块协同工作正常。 (这部分是 E2E 测试的核心内容)

## 9. 遵循 `5.testing/general.mdc` 中的通用测试规范。



