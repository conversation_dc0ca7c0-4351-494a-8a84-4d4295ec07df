---
description: 当执行测试任务时
globs: 
alwaysApply: false
---
# 通用测试规范 (General Testing)

## 1. 测试的重要性
- **质量保证**: 测试是确保软件质量、减少 bug、提高系统稳定性的关键手段。
- **信心构建**: 良好的测试覆盖能够让开发团队更有信心地进行代码重构和功能迭代。
- **文档作用**: 清晰的测试用例在某种程度上也扮演了"活文档"的角色，描述了代码单元或功能的预期行为。

## 2. 测试的基本原则 (FIRST)
- **Fast (快速)**: 测试应该运行得足够快，以便开发者可以频繁地运行它们。
- **Independent/Isolated (独立/隔离)**: 测试之间不应有依赖，一个测试的失败不应影响其他测试的执行。每个测试应有自己的数据，并在执行后进行清理（如果需要）。
- **Repeatable (可重复)**: 测试在任何环境（开发、CI、本地）中都应产生相同的结果。避免依赖外部环境的易变状态。
- **Self-Validating (自我验证)**: 测试应能自动判断其是否通过，无需人工检查输出。测试结果应该是明确的布尔值（通过/失败）。
- **Timely (及时)**: 测试代码应该与生产代码一起编写，或者在生产代码之前编写（如 TDD）。不应在功能完成后很久才补充测试。

## 3. 测试类型 (Test Pyramid - 测试金字塔)
- **单元测试 (Unit Tests)**: 
    - 针对最小的可测试单元（函数、方法、类）。
    - 运行速度快，数量多。
    - 应覆盖大部分代码逻辑。
- **集成测试 (Integration Tests)**: 
    - 测试多个模块或组件协同工作的情况（例如，服务与数据库、API 端点与业务逻辑）。
    - 运行速度中等，数量适中。
- **端到端测试 (End-to-End Tests)**: 
    - 从用户视角测试整个应用的完整流程（例如，UI 交互到数据库）。
    - 运行速度慢，数量少，维护成本高，但能提供最高的信心。
- 优先编写更多的低级别测试（单元测试），以快速反馈和定位问题。

## 4. 测试用例编写
- **AAA 模式 (Arrange, Act, Assert)**:
    - **Arrange (准备)**: 设置测试所需的初始状态和输入数据。
    - **Act (执行)**: 调用被测试的代码或执行操作。
    - **Assert (断言)**: 验证执行结果是否符合预期。
- **清晰的命名**: 测试函数/方法名应清晰地描述其测试的场景和预期结果 (例如 `test_user_login_with_valid_credentials_should_succeed`)。
- **单一职责**: 每个测试用例应专注于测试一个特定的行为或场景。
- **可读性**: 测试代码也应像生产代码一样易于阅读和维护。
- **避免测试实现细节**: 应测试公共接口和行为，而不是内部实现。这使得代码重构时测试不易失败。

## 5. 测试覆盖率
- 虽然不是唯一标准，但代码覆盖率是衡量测试全面性的一个有用指标。
- **参考 `5.testing/coverage.mdc` 规则。**

## 6. 何时不写测试 (或简化测试)
- 外部库的直接功能 (信任库本身的测试)。
- 自动生成的代码 (如 getter/setter，除非有自定义逻辑)。
- 非常简单的、几乎不可能出错的代码，且修改频率极低。
- 临时性的调试代码 (应在提交前移除)。

## 7. 测试的维护
- 测试代码与生产代码同等重要，需要持续维护和更新。
- 当生产代码变更时，相关的测试也必须更新以反映这些变更。
- 定期审查和重构测试代码，移除过时或冗余的测试。

## 8. CI/CD 集成
- 所有类型的自动化测试都应集成到持续集成/持续部署 (CI/CD) 流水线中。
- 测试失败应阻止代码合并或部署，以确保代码质量。



