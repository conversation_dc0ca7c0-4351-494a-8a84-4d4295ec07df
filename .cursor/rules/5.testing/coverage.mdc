---
description: 当执行测试任务时
globs: 
alwaysApply: false
---
# 代码覆盖率规范 (Code Coverage)

## 1. 目标与重要性
- **目标**: 旨在通过量化代码被测试执行的程度，来评估测试的全面性。
- **重要性**:
    - 帮助识别未经测试的代码区域，这些区域可能隐藏 bug。
    - 提高对代码变更的信心，因为高覆盖率意味着大部分代码行为都经过验证。
    - 不能保证没有 bug，但可以作为测试质量的一个重要指标。

## 2. 覆盖率工具
- **Python (Backend)**:
    - `coverage.py` (通常通过 `pytest-cov` 插件与 pytest 集成)。
    - 报告可以生成为 HTML（易于浏览）或 XML（用于 CI 集成）。
- **JavaScript/TypeScript (Frontend)**:
    - `Jest` 内置了代码覆盖率功能 (基于 `istanbul`)。
    - `Vitest` 也内置了代码覆盖率功能 (基于 `istanbul` 或 `v8`)。
    - 配置通常在 `jest.config.js` 或 `vitest.config.ts` 中。

## 3. 覆盖率指标
- **行覆盖率 (Line Coverage)**: 被测试执行的代码行数占总可执行代码行数的百分比。
- **分支覆盖率 (Branch Coverage)**: 在条件语句中 (如 `if`, `switch`)，每个可能的分支路径被执行的百分比。通常比行覆盖率更难达到，但也更重要。
- **函数/方法覆盖率 (Function/Method Coverage)**: 被调用的函数/方法数量占总函数/方法数量的百分比。

## 4. 覆盖率目标 (建议)
- **理想目标**: 追求尽可能高的覆盖率，例如 **80-90%** 或更高，特别是对于核心业务逻辑和关键模块。
- **最低标准**: 项目应设定一个最低可接受的覆盖率阈值 (例如 70%)，低于该阈值则构建失败或产生警告。
- **实际考量**: 
    - 100% 覆盖率并非总是实际或经济的，特别是一些难以测试的边缘情况或 UI 细节。
    - 关注关键业务逻辑的覆盖，而不是盲目追求数字。
    - 对于简单的、几乎不可能出错的代码 (如纯数据类)，覆盖率要求可以适当放宽。

## 5. 提升与维护覆盖率
- **编写全面的测试用例**: 确保测试覆盖正常路径、边界条件和错误情况。
- **定期审查覆盖率报告**: 
    - 在代码审查过程中检查新增代码的覆盖率。
    - 定期分析整体覆盖率报告，找出未覆盖或覆盖不足的区域。
- **增量维护**: 新功能开发和 bug 修复时，必须同步编写或更新测试用例，以保持或提高覆盖率。
- **忽略不相关代码**: 配置覆盖率工具忽略不应被测试的代码，例如：
    - 自动生成的代码。
    - 第三方库的 vendored 代码。
    - 简单的配置文件或数据结构声明。
    - 调试代码或临时代码 (应在提交前移除)。
    - (Python 示例: `pragma: no cover` 注释, 或在配置文件中排除特定文件/目录)。
    - (JS/TS 示例: `/* istanbul ignore next */` 注释)。

## 6. CI/CD 集成
- **自动生成报告**: 在 CI/CD 流水线中自动运行测试并生成覆盖率报告。
- **质量门禁 (Quality Gate)**: 可以设置覆盖率阈值作为质量门禁，如果未达到则阻止合并或部署。
- **历史趋势**: 一些 CI 工具可以跟踪覆盖率的历史趋势。

## 7. 注意事项
- **覆盖率不是银弹**: 高覆盖率不等于高质量代码或无 bug 应用。测试用例本身的质量 (断言是否有效、场景是否真实) 更为重要。
- **避免为覆盖率而写测试**: 不要编写仅为提高覆盖率数字而没有实际验证价值的测试。

## 8. 遵循 `5.testing/general.mdc` 中的通用测试规范。



