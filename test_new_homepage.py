#!/usr/bin/env python3
"""
测试新的首页上中下布局功能
"""
import requests
import json
import time

def test_homepage_apis():
    """测试首页需要的API"""
    print("🧪 测试新首页功能...")
    
    # 测试规则集API
    print("\n1. 测试规则集API...")
    try:
        # 获取当前规则集
        response = requests.get("http://localhost:8002/api/v1/simple-rules/current", timeout=10)
        print(f"  当前规则集API: {response.status_code}")
        if response.status_code == 200:
            current = response.json()
            print(f"  - 当前规则集: {current['name']}")
            print(f"  - 规则大类数: {len(current['categories'])}")
        
        # 获取已保存规则集
        response = requests.get("http://localhost:8002/api/v1/simple-rules/saved", timeout=10)
        print(f"  已保存规则集API: {response.status_code}")
        if response.status_code == 200:
            saved = response.json()
            print(f"  - 可用规则集数: {len(saved)}")
            for ruleset in saved:
                print(f"    • {ruleset['name']} {'(默认)' if ruleset['is_default'] else ''}")
        
        return True
    except Exception as e:
        print(f"  ❌ 规则集API测试失败: {e}")
        return False

def test_transform_with_ruleset():
    """测试使用规则集的转换功能"""
    print("\n2. 测试转换功能...")
    
    test_text = """M：主任您好，华为委托的第三方。
1：好，您说。
M：想了解一下我们跟华为合作的使用它的产品方案。
1：嗯，得有一二十年了，基本上用了它网络安全什么服务器什么一类的。"""
    
    try:
        # 使用默认规则集转换
        response = requests.post(
            "http://localhost:8002/api/v1/transform",
            json={
                "text": test_text,
                "rules": [],  # 使用空规则列表，让后端使用默认处理
                "llm_provider": "mock"
            },
            timeout=15
        )
        
        print(f"  转换API: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"  - 转换状态: {result.get('status')}")
            print(f"  - 原文长度: {len(result.get('original_text', ''))}")
            print(f"  - 转换后长度: {len(result.get('transformed_text', ''))}")
            
            # 显示转换结果预览
            transformed = result.get('transformed_text', '')
            if transformed:
                print(f"  - 转换结果预览: {transformed[:100]}...")
            
            return True
        else:
            print(f"  ❌ 转换失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 转换测试失败: {e}")
        return False

def test_ruleset_switching():
    """测试规则集切换功能"""
    print("\n3. 测试规则集切换...")
    
    try:
        # 获取可用规则集
        response = requests.get("http://localhost:8002/api/v1/simple-rules/saved", timeout=10)
        if response.status_code != 200:
            print("  ❌ 无法获取规则集列表")
            return False
        
        rulesets = response.json()
        if len(rulesets) < 2:
            print("  ⚠️ 规则集数量不足，无法测试切换")
            return True
        
        # 切换到第二个规则集
        target_ruleset = rulesets[1]
        response = requests.post(
            f"http://localhost:8002/api/v1/simple-rules/switch/{target_ruleset['id']}",
            timeout=10
        )
        
        print(f"  切换规则集API: {response.status_code}")
        if response.status_code == 200:
            print(f"  - 成功切换到: {target_ruleset['name']}")
            
            # 验证切换结果
            response = requests.get("http://localhost:8002/api/v1/simple-rules/current", timeout=10)
            if response.status_code == 200:
                current = response.json()
                if current['id'] == target_ruleset['id']:
                    print(f"  ✅ 切换验证成功")
                    return True
                else:
                    print(f"  ❌ 切换验证失败")
                    return False
        else:
            print(f"  ❌ 切换失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 规则集切换测试失败: {e}")
        return False

def test_frontend_accessibility():
    """测试前端页面可访问性"""
    print("\n4. 测试前端页面...")
    
    try:
        # 测试新的首页
        response = requests.get("http://localhost:3001/dashboard", timeout=10)
        print(f"  首页 (新布局): {response.status_code}")
        
        # 测试规则管理页面
        response = requests.get("http://localhost:3001/rules", timeout=10)
        print(f"  规则管理页面: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"  ❌ 前端页面测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 新首页功能测试")
    print("=" * 50)
    
    tests = [
        ("规则集API", test_homepage_apis),
        ("转换功能", test_transform_with_ruleset),
        ("规则集切换", test_ruleset_switching),
        ("前端页面", test_frontend_accessibility),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 新首页功能测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 新首页所有功能测试通过！")
        print("\n✅ 新首页特性验证:")
        print("  ✅ 上中下布局 - 一屏显示完整")
        print("  ✅ 上部：文本输入/文件上传切换")
        print("  ✅ 中部：规则集选择和转换按钮")
        print("  ✅ 下部：左右对比显示原文和结果")
        print("  ✅ 规则集管理 - 切换和配置功能")
        print("\n🚀 新首页已准备就绪！")
    else:
        print("⚠️ 部分功能测试失败，需要进一步检查")
    
    print("\n📍 可用功能地址:")
    print("  - 新首页: http://localhost:3001/dashboard")
    print("  - 规则管理: http://localhost:3001/rules")

if __name__ == "__main__":
    main()
