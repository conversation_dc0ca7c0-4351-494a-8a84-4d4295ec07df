# 🚀 快速启动指南

## 📋 环境要求
- **Node.js**: 18.0+
- **Python**: 3.11+
- **操作系统**: macOS/Linux/Windows

## ⚡ 快速启动

### 1. 启动后端服务
```bash
# 进入后端目录
cd backend

# 激活虚拟环境并启动服务
source venv/bin/activate
python simple_server.py
```

**后端服务地址**: http://localhost:8001  
**API文档**: http://localhost:8001/docs  
**健康检查**: http://localhost:8001/api/v1/health  

### 2. 启动前端服务
```bash
# 新开终端，进入前端目录
cd frontend

# 启动开发服务器
npm run dev
```

**前端服务地址**: http://localhost:3001

## 🧪 功能测试

### 基础功能测试
1. **访问首页**: http://localhost:3001
2. **进入主应用**: 点击"开始使用"按钮
3. **测试转换功能**:
   - 在左侧输入框粘贴测试文本
   - 点击"🚀 开始转换"按钮
   - 查看右侧转换结果和质量指标

### 测试数据
可以使用项目中的训练数据进行测试：
```
M：主任您好，华为委托的第三方，不好意思，刚才信号不好断掉了。
1：好，您说。
M：想了解一下我们跟华为合作的使用它的产品方案，整个有感知的方面。
1：得有一二十年了。
M：都用的是哪些产品？
1：基本上用了它网络安全什么服务器什么一类的，基本上全用了。
```

## 🔧 API测试

### 健康检查
```bash
curl http://localhost:8001/api/v1/health
```

### 文本转换
```bash
curl -X POST http://localhost:8001/api/v1/transform \
  -H "Content-Type: application/json" \
  -d '{"text":"M：您好\n1：好的，我来回答"}'
```

## 📊 预期结果

### 转换效果
- **输入**: 对话式笔录 (M: 访谈者, 1: 被访者)
- **输出**: 第一人称叙述文档
- **质量指标**: 字数保留率、原文引用率、实质保持率

### 界面功能
- ✅ 响应式设计，支持桌面和移动端
- ✅ 实时字数统计
- ✅ 转换状态显示
- ✅ 质量指标可视化
- ✅ 错误提示和处理

## 🐛 常见问题

### 端口冲突
- 前端默认端口3000被占用时，会自动使用3001
- 后端使用8001端口，如冲突请修改simple_server.py中的端口

### 依赖问题
- 确保Node.js版本18+
- 确保Python版本3.11+
- 如遇到包安装问题，请检查网络连接

### CORS错误
- 确保后端服务正常运行
- 检查前端API调用地址是否正确

## 📞 技术支持

如遇到问题，请检查：
1. 服务是否正常启动
2. 端口是否被占用
3. 依赖是否正确安装
4. 网络连接是否正常

## 🎯 下一步

阶段一完成后，可以：
1. 体验基础转换功能
2. 查看API文档了解接口设计
3. 准备进入阶段二开发
4. 集成真实的LLM API
