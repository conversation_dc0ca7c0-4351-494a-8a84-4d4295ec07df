# 开发指南

## 💻 项目结构

### 前端项目结构
```
frontend/
├── app/                    # Next.js App Router 结构
│   ├── (auth)/            # 认证相关路由组
│   │   ├── login/
│   │   └── register/
│   ├── dashboard/         # 主应用界面
│   ├── rules/            # 规则配置界面
│   ├── result/           # 结果展示界面
│   ├── globals.css       # 全局样式
│   ├── layout.tsx        # 根布局组件
│   └── page.tsx          # 首页组件
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件 (shadcn/ui)
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── card.tsx
│   │   └── ...
│   ├── layout/           # 布局相关组件
│   │   ├── header.tsx
│   │   ├── sidebar.tsx
│   │   └── navigation.tsx
│   ├── forms/            # 表单组件
│   │   ├── upload-form.tsx
│   │   ├── login-form.tsx
│   │   └── rule-config-form.tsx
│   ├── features/         # 功能特定组件
│   │   ├── text-upload/
│   │   ├── rule-manager/
│   │   ├── result-viewer/
│   │   └── history/
│   └── common/           # 通用组件
│       ├── loading.tsx
│       ├── error-boundary.tsx
│       └── modal.tsx
├── lib/                  # 工具库和配置
│   ├── utils.ts          # 通用工具函数
│   ├── api.ts            # API 客户端
│   ├── auth.ts           # 认证相关
│   ├── constants.ts      # 常量定义
│   └── types.ts          # TypeScript 类型定义
├── hooks/                # 自定义 React Hooks
│   ├── useAuth.ts
│   ├── useUpload.ts
│   ├── useRules.ts
│   └── useHistory.ts
├── contexts/             # React Context
│   ├── AuthContext.tsx
│   ├── RulesContext.tsx
│   └── ThemeContext.tsx
├── public/               # 静态资源
│   ├── images/
│   └── icons/
├── styles/               # 样式文件
│   └── components/       # 组件专用样式
├── __tests__/            # 测试文件
│   ├── components/
│   ├── pages/
│   └── utils/
├── .env.example          # 环境变量示例
├── next.config.js        # Next.js 配置
├── tailwind.config.js    # Tailwind CSS 配置
├── tsconfig.json         # TypeScript 配置
└── package.json          # 依赖管理
```

### 后端项目结构
```
backend/
├── app/                  # FastAPI 应用主体
│   ├── api/             # API 路由
│   │   ├── v1/          # API 版本 1
│   │   │   ├── auth.py      # 认证相关API
│   │   │   ├── upload.py    # 文件上传API
│   │   │   ├── rules.py     # 规则管理API
│   │   │   ├── transform.py # 转换处理API
│   │   │   └── history.py   # 历史记录API
│   │   └── deps.py      # API 依赖项
│   ├── core/            # 核心功能模块
│   │   ├── config.py    # 配置管理
│   │   ├── security.py  # 安全相关
│   │   ├── database.py  # 数据库配置
│   │   └── exceptions.py # 异常处理
│   ├── models/          # 数据模型
│   │   ├── user.py      # 用户模型
│   │   ├── rule.py      # 规则模型
│   │   ├── transform.py # 转换记录模型
│   │   └── history.py   # 历史记录模型
│   ├── schemas/         # Pydantic 数据校验
│   │   ├── user.py
│   │   ├── rule.py
│   │   ├── transform.py
│   │   └── common.py
│   ├── services/        # 业务逻辑服务
│   │   ├── auth_service.py
│   │   ├── file_service.py
│   │   ├── rule_service.py
│   │   ├── transform_service.py
│   │   └── llm_service.py
│   ├── transformers/    # 转换引擎
│   │   ├── base.py      # 基础转换器
│   │   ├── rule_engine.py   # 规则引擎
│   │   ├── llm_engine.py    # LLM 处理引擎
│   │   ├── preprocessor.py  # 预处理器
│   │   └── postprocessor.py # 后处理器
│   ├── utils/           # 工具函数
│   │   ├── file_utils.py
│   │   ├── text_utils.py
│   │   ├── validation.py
│   │   └── metrics.py
│   └── main.py          # FastAPI 应用入口
├── tests/               # 测试文件
│   ├── api/
│   ├── services/
│   ├── transformers/
│   └── conftest.py
├── alembic/             # 数据库迁移
│   ├── versions/
│   ├── env.py
│   └── script.py.mako
├── requirements.txt     # Python 依赖
├── pyproject.toml       # Poetry 配置
├── .env.example         # 环境变量示例
└── Dockerfile           # Docker 配置
```

## 🚀 开发环境设置

### 前端开发环境
```bash
# 1. 克隆项目
git clone <repository-url>
cd frontend

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 配置后端API地址等

# 4. 启动开发服务器
npm run dev

# 5. 构建生产版本
npm run build

# 6. 启动生产服务器
npm start
```

### 后端开发环境
```bash
# 1. 进入后端目录
cd backend

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 3. 安装依赖
pip install -r requirements.txt
# 或使用 Poetry
poetry install

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 配置数据库、LLM API等

# 5. 初始化数据库
alembic upgrade head

# 6. 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 本地 LLM 环境设置 (可选)
```bash
# 1. 安装 Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. 下载推荐模型
ollama pull qwen2.5:7b-instruct

# 3. 启动 Ollama 服务
ollama serve

# 4. 在后端配置中启用本地模式
# .env 文件中设置:
LLM_PROVIDER=ollama
OLLAMA_MODEL=qwen2.5:7b-instruct
OLLAMA_BASE_URL=http://localhost:11434
```

## 📋 代码规范

### TypeScript/JavaScript 规范 (前端)
```json
// .eslintrc.json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

**命名规范**:
- **组件**: PascalCase (例: `UploadForm.tsx`)
- **函数/变量**: camelCase (例: `getUserData`)
- **常量**: SCREAMING_SNAKE_CASE (例: `API_BASE_URL`)
- **文件**: kebab-case 或 camelCase
- **类型/接口**: PascalCase (例: `UserProfile`)

**组件编写规范**:
```typescript
// 优秀的组件结构示例
interface Props {
  title: string;
  onSubmit: (data: FormData) => void;
  isLoading?: boolean;
}

export const UploadForm: React.FC<Props> = ({ 
  title, 
  onSubmit, 
  isLoading = false 
}) => {
  // Hooks 声明
  const [file, setFile] = useState<File | null>(null);
  
  // 事件处理函数
  const handleSubmit = useCallback((e: FormEvent) => {
    e.preventDefault();
    if (file) {
      onSubmit(new FormData());
    }
  }, [file, onSubmit]);

  // 渲染
  return (
    <form onSubmit={handleSubmit}>
      {/* JSX 内容 */}
    </form>
  );
};
```

### Python 代码规范 (后端)
```toml
# pyproject.toml - Ruff 配置
[tool.ruff]
line-length = 88
target-version = "py311"

[tool.ruff.lint]
select = ["E", "F", "W", "C90", "I", "N", "D", "UP", "B", "A", "S", "T20", "Q"]
ignore = ["D100", "D101", "D102", "D103", "D104", "D105"]

[tool.ruff.lint.pydocstyle]
convention = "google"
```

**命名规范**:
- **函数/变量**: snake_case (例: `get_user_data`)
- **类**: PascalCase (例: `UserService`)
- **常量**: SCREAMING_SNAKE_CASE (例: `API_KEY`)
- **私有方法**: 前缀下划线 (例: `_validate_input`)
- **模块**: snake_case (例: `user_service.py`)

**函数编写规范**:
```python
from typing import Optional, List
from pydantic import BaseModel

async def transform_text(
    text: str,
    rules: List[str],
    llm_provider: Optional[str] = None
) -> dict[str, any]:
    """
    转换文本内容基于指定规则.
    
    Args:
        text: 待转换的文本内容
        rules: 应用的规则列表
        llm_provider: LLM提供商，None时使用默认配置
        
    Returns:
        包含转换结果和元数据的字典
        
    Raises:
        ValidationError: 当输入参数无效时
        TransformError: 当转换过程失败时
    """
    # 实现逻辑...
    pass
```

## 🧪 测试策略

### 前端测试
```bash
# 安装测试依赖
npm install --save-dev @testing-library/react @testing-library/jest-dom jest-environment-jsdom

# 运行测试
npm run test        # 单次运行
npm run test:watch  # 监听模式
npm run test:coverage  # 覆盖率报告
```

**组件测试示例**:
```typescript
// __tests__/components/UploadForm.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { UploadForm } from '@/components/forms/UploadForm';

describe('UploadForm', () => {
  it('应该正确渲染上传表单', () => {
    const mockOnSubmit = jest.fn();
    
    render(
      <UploadForm 
        title="测试标题" 
        onSubmit={mockOnSubmit} 
      />
    );
    
    expect(screen.getByText('测试标题')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /上传/i })).toBeInTheDocument();
  });

  it('应该在文件选择后启用提交按钮', async () => {
    const mockOnSubmit = jest.fn();
    
    render(
      <UploadForm 
        title="测试标题" 
        onSubmit={mockOnSubmit} 
      />
    );
    
    const fileInput = screen.getByRole('input', { hidden: true });
    const file = new File(['测试内容'], 'test.txt', { type: 'text/plain' });
    
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    const submitButton = screen.getByRole('button', { name: /上传/i });
    expect(submitButton).not.toBeDisabled();
  });
});
```

### 后端测试
```bash
# 运行测试
pytest                    # 运行所有测试
pytest --cov=app         # 生成覆盖率报告
pytest -v tests/api/     # 运行特定目录测试
pytest -k "test_upload"  # 运行特定测试
```

**API测试示例**:
```python
# tests/api/test_upload.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestUploadAPI:
    """文件上传API测试"""
    
    def test_upload_text_file_success(self):
        """测试文本文件上传成功"""
        test_content = "测试用户: 这是一段测试对话\n访谈者: 好的，继续"
        
        response = client.post(
            "/api/v1/upload",
            files={"file": ("test.txt", test_content, "text/plain")},
            headers={"Authorization": "Bearer valid_token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "file_id" in data
        assert data["content_preview"].startswith("测试用户:")

    def test_upload_invalid_file_format(self):
        """测试无效文件格式上传"""
        response = client.post(
            "/api/v1/upload",
            files={"file": ("test.pdf", b"invalid content", "application/pdf")},
            headers={"Authorization": "Bearer valid_token"}
        )
        
        assert response.status_code == 400
        assert "不支持的文件格式" in response.json()["detail"]
        
    def test_upload_without_auth(self):
        """测试未认证上传"""
        response = client.post(
            "/api/v1/upload",
            files={"file": ("test.txt", "content", "text/plain")}
        )
        
        assert response.status_code == 401
```

**服务层测试示例**:
```python
# tests/services/test_transform_service.py
import pytest
from unittest.mock import Mock, patch
from app.services.transform_service import TransformService

class TestTransformService:
    """转换服务测试"""
    
    @pytest.fixture
    def transform_service(self):
        return TransformService()
    
    @patch('app.services.transform_service.LLMService')
    async def test_transform_text_with_llm(self, mock_llm, transform_service):
        """测试使用LLM的文本转换"""
        # 模拟LLM响应
        mock_llm.return_value.process_text.return_value = "转换后的文本"
        
        result = await transform_service.transform_text(
            text="原始文本",
            rules=["rule1", "rule2"],
            use_llm=True
        )
        
        assert result["transformed_text"] == "转换后的文本"
        assert result["rules_applied"] == ["rule1", "rule2"]
        mock_llm.return_value.process_text.assert_called_once()
```

### E2E 测试 (Playwright)
```bash
# 安装 Playwright
npm install --save-dev @playwright/test

# 安装浏览器
npx playwright install

# 运行 E2E 测试
npm run test:e2e
npx playwright test --ui  # 带UI的测试运行器
```

**E2E测试示例**:
```typescript
// tests/e2e/upload-workflow.spec.ts
import { test, expect } from '@playwright/test';

test.describe('文件上传工作流', () => {
  test('完整的文件上传和转换流程', async ({ page }) => {
    // 1. 登录
    await page.goto('/login');
    await page.fill('[data-testid=email]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'password');
    await page.click('[data-testid=login-button]');
    
    // 2. 上传文件
    await page.goto('/dashboard');
    await page.setInputFiles('[data-testid=file-input]', 'test-data/sample.txt');
    
    // 3. 等待文件处理
    await expect(page.locator('[data-testid=file-preview]')).toBeVisible();
    
    // 4. 配置规则
    await page.click('[data-testid=rules-tab]');
    await page.check('[data-testid=rule-language-optimization]');
    
    // 5. 开始转换
    await page.click('[data-testid=transform-button]');
    
    // 6. 验证结果
    await expect(page.locator('[data-testid=transform-result]')).toBeVisible();
    await expect(page.locator('[data-testid=metrics-card]')).toBeVisible();
    
    // 7. 下载结果
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid=download-button]');
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/^转换后笔录_\d+\.txt$/);
  });
});
```

## 🛠️ 开发工具配置

### VS Code 推荐设置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  }
}
```

### Git 钩子配置
```bash
# 安装 husky
npm install --save-dev husky lint-staged

# 配置 pre-commit 钩子
npx husky add .husky/pre-commit "npx lint-staged"
```

```json
// package.json
{
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{py}": [
      "ruff format",
      "ruff check --fix"
    ]
  }
}
```

---

更多信息请参考：
- [技术架构](README_architecture.md)
- [用户指南](README_user_guide.md)
- [部署指南](README_deployment.md) 