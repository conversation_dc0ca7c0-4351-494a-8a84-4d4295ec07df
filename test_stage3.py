#!/usr/bin/env python3
"""
阶段三规则引擎测试脚本
"""
import requests
import json

def test_rule_templates():
    """测试规则模板API"""
    try:
        response = requests.get("http://localhost:8002/api/v1/rules/templates")
        print(f"规则模板API: {response.status_code}")
        if response.status_code == 200:
            templates = response.json()
            print(f"  - 模板数量: {len(templates)}")
            for template in templates[:3]:  # 显示前3个
                print(f"  - {template['name']}: {template['description']}")
        return response.status_code == 200
    except Exception as e:
        print(f"规则模板API失败: {e}")
        return False

def test_rule_execution():
    """测试规则执行API"""
    try:
        test_text = """M：您好，请问您对华为的产品有什么看法？
1：我觉得华为的产品还是不错的，技术比较先进。
M：那您觉得有什么需要改进的地方吗？
1：嗯，我觉得价格可以再便宜一些。"""
        
        data = {
            "text": test_text
        }
        
        response = requests.post(
            "http://localhost:8002/api/v1/rules/execute",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"规则执行API: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"  - 转换成功: {result.get('success', False)}")
            print(f"  - 应用规则数: {len(result.get('rules_applied', []))}")
            print(f"  - 原文长度: {len(result.get('original_text', ''))}")
            print(f"  - 转换后长度: {len(result.get('transformed_text', ''))}")
            print(f"  - 转换结果预览: {result.get('transformed_text', '')[:100]}...")
        else:
            print(f"  - 错误: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"规则执行API失败: {e}")
        return False

def test_text_transform_with_rules():
    """测试集成的文本转换API（使用规则引擎）"""
    try:
        test_text = """M：主任您好，华为委托的第三方。
1：好，您说。
M：想了解一下我们跟华为合作的使用它的产品方案。
1：得有一二十年了，基本上用了它网络安全什么服务器什么一类的。"""
        
        data = {
            "text": test_text,
            "rules": [],
            "llm_provider": "mock"
        }
        
        response = requests.post(
            "http://localhost:8002/api/v1/transform",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"集成转换API: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"  - 转换状态: {result.get('status', '')}")
            print(f"  - 应用规则: {result.get('rules_applied', [])}")
            print(f"  - 转换结果: {result.get('transformed_text', '')[:100]}...")
        else:
            print(f"  - 错误: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"集成转换API失败: {e}")
        return False

def test_rule_categories():
    """测试规则分类API"""
    try:
        response = requests.get("http://localhost:8002/api/v1/rules/templates/categories")
        print(f"规则分类API: {response.status_code}")
        if response.status_code == 200:
            categories = response.json()
            print(f"  - 分类数量: {len(categories)}")
            for category, templates in categories.items():
                print(f"  - {category}: {len(templates)} 个模板")
        return response.status_code == 200
    except Exception as e:
        print(f"规则分类API失败: {e}")
        return False

def test_health_check():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:8002/api/v1/health")
        print(f"健康检查: {response.status_code}")
        if response.status_code == 200:
            health = response.json()
            print(f"  - 状态: {health.get('status')}")
            print(f"  - 版本: {health.get('version')}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 阶段三规则引擎测试开始...")
    print("=" * 50)
    
    tests = [
        ("健康检查", test_health_check),
        ("规则模板API", test_rule_templates),
        ("规则分类API", test_rule_categories),
        ("规则执行API", test_rule_execution),
        ("集成转换API", test_text_transform_with_rules),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！阶段三规则引擎系统运行正常！")
    else:
        print("⚠️  部分测试失败，请检查系统配置")
    
    print("\n📍 可用的API端点:")
    print("  - 规则模板: http://localhost:8002/api/v1/rules/templates")
    print("  - 规则分类: http://localhost:8002/api/v1/rules/templates/categories")
    print("  - 规则执行: http://localhost:8002/api/v1/rules/execute")
    print("  - API文档: http://localhost:8002/docs")
