#!/usr/bin/env python3
"""
阶段一功能测试脚本
测试前后端基础功能是否正常工作
"""
import requests
import json
import time

# 配置
BACKEND_URL = "http://localhost:8001"
FRONTEND_URL = "http://localhost:3001"

def test_backend_health():
    """测试后端健康检查"""
    print("🔍 测试后端健康检查...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端健康检查通过: {data['status']}")
            print(f"   版本: {data['version']}")
            print(f"   时间: {data['timestamp']}")
            return True
        else:
            print(f"❌ 后端健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_transform_api():
    """测试文本转换API"""
    print("\n🔍 测试文本转换API...")
    
    # 使用训练数据进行测试
    test_text = """M：主任您好，华为委托的第三方，不好意思，刚才信号不好断掉了。
1：好，您说。
M：想了解一下我们跟华为合作的使用它的产品方案，整个有感知的方面，对他的过去这一年的一个感知和评价。
1：得有一二十年了。
M：都用的是哪些产品？
1：基本上用了它网络安全什么服务器什么一类的，基本上全用了，这几大品类全用了。"""
    
    payload = {
        "text": test_text,
        "rules": ["speaker_identification", "language_optimization"],
        "llm_provider": "mock"
    }
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/v1/transform",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 文本转换API测试通过")
            print(f"   转换ID: {data['id']}")
            print(f"   状态: {data['status']}")
            print(f"   原文长度: {len(data['original_text'])} 字符")
            print(f"   转换后长度: {len(data['transformed_text'])} 字符")
            print(f"   字数保留率: {data['metrics']['word_retention_rate']:.2%}")
            print(f"   原文引用率: {data['metrics']['quote_retention_rate']:.2%}")
            print(f"   实质保持率: {data['metrics']['content_preservation_rate']:.2%}")
            
            # 显示转换结果预览
            preview = data['transformed_text'][:200] + "..." if len(data['transformed_text']) > 200 else data['transformed_text']
            print(f"   转换结果预览: {preview}")
            
            return True, data['id']
        else:
            print(f"❌ 文本转换API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ 文本转换API连接失败: {e}")
        return False, None

def test_get_result_api(record_id):
    """测试获取转换结果API"""
    print(f"\n🔍 测试获取转换结果API (ID: {record_id})...")
    
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/transform/{record_id}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取转换结果API测试通过")
            print(f"   记录ID: {data['id']}")
            print(f"   状态: {data['status']}")
            print(f"   创建时间: {data['created_at']}")
            return True
        else:
            print(f"❌ 获取转换结果API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取转换结果API连接失败: {e}")
        return False

def test_frontend_access():
    """测试前端访问"""
    print(f"\n🔍 测试前端访问...")
    
    try:
        response = requests.get(FRONTEND_URL)
        if response.status_code == 200:
            print("✅ 前端页面访问正常")
            print(f"   状态码: {response.status_code}")
            return True
        else:
            print(f"❌ 前端页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始阶段一功能测试")
    print("=" * 50)
    
    # 测试结果统计
    tests = []
    
    # 1. 测试后端健康检查
    tests.append(("后端健康检查", test_backend_health()))
    
    # 2. 测试文本转换API
    transform_success, record_id = test_transform_api()
    tests.append(("文本转换API", transform_success))
    
    # 3. 测试获取结果API
    if record_id:
        tests.append(("获取转换结果API", test_get_result_api(record_id)))
    
    # 4. 测试前端访问
    tests.append(("前端页面访问", test_frontend_access()))
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 阶段一所有功能测试通过！")
        print("\n📋 阶段一交付清单:")
        print("✅ 前端Next.js项目初始化完成")
        print("✅ 后端FastAPI项目初始化完成")
        print("✅ 基础UI界面实现完成")
        print("✅ 文本转换API实现完成")
        print("✅ 健康检查API实现完成")
        print("✅ 前后端通信建立完成")
        print("✅ 基础转换功能演示完成")
        print("\n🎯 可以进入阶段二开发！")
    else:
        print("⚠️  部分功能测试失败，需要修复后再进入下一阶段")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
