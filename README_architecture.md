# 技术架构详细说明

## 🏗️ 详细技术架构

### 前端技术栈
- **核心框架**: React 18.2.0 + Next.js 15.0.3 (App Router)
- **开发语言**: TypeScript 5.0+
- **样式方案**: Tailwind CSS 3.4+
- **UI组件库**: Radix UI + shadcn/ui
- **状态管理**: React Context API + URL 搜索参数
- **图标库**: Lucide React
- **包管理**: npm
- **代码质量**: ESLint + Prettier

### 后端技术栈
- **编程语言**: Python 3.11.5
- **Web 框架**: FastAPI 0.104.1
- **LLM 集成**:
  - **云端方案**: Deepseek API
  - **本地方案**: Ollama + Qwen2.5-7B-Instruct (推荐配置)
  - **Hugging Face**: `transformers` 库支持
- **文档处理**: python-docx 1.1.0+ (Word文档读写)
- **依赖管理**: Poetry 1.6+
- **数据校验**: Pydantic 2.0+
- **异步处理**: asyncio + uvicorn 0.24+

### 数据存储
- **开发环境**: SQLite 3.40+
- **生产环境**: PostgreSQL 15+ (推荐)
- **ORM**: SQLAlchemy 2.0+
- **数据迁移**: Alembic 1.12+

### 混合处理架构设计

#### 确定性规则引擎
- **发言人标记识别**: 正则表达式 + 启发式算法
- **格式化处理**: 标准化文本预处理管道
- **语法修正**: 基于规则的语法检查和修正
- **适用场景**: 简单重复、确定性强的转换任务

#### LLM 处理引擎
- **语言优化**: 基于上下文的语言流畅性改进
- **语气调整**: 保持口语化感觉的同时提升可读性
- **上下文理解**: 跨句子和段落的语义连贯性处理
- **视角转换**: 从对话式转换为第一人称叙述
- **适用场景**: 复杂语义理解和生成任务

#### 混合处理流程
```
输入文本 → 预处理 → 确定性规则处理 → LLM优化 → 后处理 → 输出文本
         ↓
    发言人识别
    格式标准化     → 语言优化 → 质量检验
    基础修正         语气调整    指标计算
                    视角转换
```

### 开发工具与 MCP 集成

#### MCP (Model Context Protocol) 集成说明
- **21st-dev/magic MCP**: UI 组件快速生成和优化
  - 使用场景: 快速构建React组件和界面
  - 集成方式: VS Code + Cursor 扩展
  
- **Sequential-Thinking MCP**: 复杂问题分析和方案设计
  - 使用场景: 架构设计、问题诊断、方案评估
  - 集成方式: 开发过程中的决策支持工具
  
- **Playwright MCP**: 自动化测试执行和反馈
  - 使用场景: E2E测试、回归测试、用户流程验证
  - 集成方式: CI/CD 管道中的自动化测试
  
- **Context-7 MCP**: 文档查询和代码生成辅助
  - 使用场景: 技术文档查询、API使用指导
  - 集成方式: 开发环境中的实时文档助手

### 安全与质量保证

#### 输入验证与安全
- **文件格式验证**: 严格的MIME类型检查和文件头验证
- **内容大小限制**: 防止DoS攻击的文件大小和处理时间限制
- **Prompt 注入防护**: LLM输入的清理、转义和模板化处理
- **XSS防护**: 前端输入输出的严格转义和CSP策略

#### 数据安全
- **敏感数据保护**: API密钥通过环境变量管理，不提交到代码库
- **传输安全**: 生产环境强制HTTPS，开发环境支持HTTPS
- **存储安全**: 用户数据加密存储，定期备份和恢复测试
- **访问控制**: JWT令牌认证 + RBAC角色权限控制

#### 代码质量保证
- **类型安全**: TypeScript严格模式 + Python类型提示
- **代码规范**: ESLint + Prettier (前端) + Ruff (后端)
- **依赖安全**: 定期安全扫描 (npm audit, safety) 和依赖更新
- **代码审查**: 所有PR必须经过代码审查才能合并

#### 用户认证与授权
- **认证方式**: JWT (JSON Web Token) 基于的无状态认证
- **密码安全**: bcrypt哈希 + 盐值处理
- **会话管理**: 访问令牌 + 刷新令牌机制
- **权限控制**: 基于角色的访问控制 (RBAC)
  - **管理员**: 完整系统管理权限
  - **普通用户**: 个人数据和功能访问权限

#### 用户数据隐私
- **数据加密**: 敏感数据字段级加密存储
- **数据最小化**: 只收集和存储必要的用户数据
- **访问日志**: 详细的数据访问和操作日志记录
- **数据删除**: 用户数据删除和匿名化机制
- **隐私合规**: 遵循GDPR和相关隐私法规要求

### 性能优化策略

#### 前端性能
- **代码分割**: Next.js自动代码分割和懒加载
- **图片优化**: Next.js Image组件优化和WebP支持
- **缓存策略**: 浏览器缓存 + CDN缓存优化
- **Bundle优化**: Tree shaking和无用代码移除

#### 后端性能
- **异步处理**: FastAPI异步支持 + 异步数据库操作
- **连接池**: 数据库连接池优化
- **缓存层**: Redis缓存热点数据和计算结果
- **负载均衡**: 支持水平扩展的无状态设计

#### LLM处理优化
- **请求批处理**: 多个请求的批量处理优化
- **结果缓存**: 相似输入的结果缓存机制
- **模型选择**: 根据任务复杂度选择合适的模型
- **流式响应**: 支持流式输出提升用户体验

---

更多信息请参考：
- [用户指南](README_user_guide.md)
- [开发指南](README_development.md)
- [部署指南](README_deployment.md) 