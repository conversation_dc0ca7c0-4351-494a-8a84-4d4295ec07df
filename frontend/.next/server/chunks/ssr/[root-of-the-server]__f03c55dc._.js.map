{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { ChevronDownIcon, ChevronUpIcon, CheckIcon } from \"@radix-ui/react-icons\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDownIcon className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUpIcon />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDownIcon />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <CheckIcon className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,gLAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIjC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gLAAA,CAAA,gBAAa;;;;;;;;;;AAGlB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gLAAA,CAAA,kBAAe;;;;;;;;;;AAGpB,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,gLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/rules/RuleList.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Input } from \"@/components/ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\n\ninterface Rule {\n  id: string;\n  name: string;\n  description: string;\n  type: string;\n  enabled: boolean;\n  priority: number;\n  conditions: any[];\n  actions: any[];\n  tags: string[];\n  created_at?: string;\n  updated_at?: string;\n}\n\ninterface RuleListProps {\n  rules: Rule[];\n  onEdit: (rule: Rule) => void;\n  onDelete: (ruleId: string) => void;\n  onToggleEnabled: (ruleId: string, enabled: boolean) => void;\n}\n\nconst ruleTypeLabels: Record<string, string> = {\n  text_replace: \"文本替换\",\n  regex_replace: \"正则替换\",\n  speaker_filter: \"发言人过滤\",\n  line_filter: \"行过滤\",\n  format_adjust: \"格式调整\",\n  conditional: \"条件规则\"\n};\n\nconst ruleTypeColors: Record<string, string> = {\n  text_replace: \"bg-blue-100 text-blue-800\",\n  regex_replace: \"bg-purple-100 text-purple-800\",\n  speaker_filter: \"bg-green-100 text-green-800\",\n  line_filter: \"bg-yellow-100 text-yellow-800\",\n  format_adjust: \"bg-orange-100 text-orange-800\",\n  conditional: \"bg-red-100 text-red-800\"\n};\n\nexport default function RuleList({ rules, onEdit, onDelete, onToggleEnabled }: RuleListProps) {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [filterType, setFilterType] = useState<string>(\"all\");\n  const [filterEnabled, setFilterEnabled] = useState<string>(\"all\");\n  const [sortBy, setSortBy] = useState<string>(\"priority\");\n\n  // 过滤和排序规则\n  const filteredAndSortedRules = rules\n    .filter(rule => {\n      const matchesSearch = rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           rule.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n      \n      const matchesType = filterType === \"all\" || rule.type === filterType;\n      const matchesEnabled = filterEnabled === \"all\" || \n                            (filterEnabled === \"enabled\" && rule.enabled) ||\n                            (filterEnabled === \"disabled\" && !rule.enabled);\n      \n      return matchesSearch && matchesType && matchesEnabled;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case \"priority\":\n          return a.priority - b.priority;\n        case \"name\":\n          return a.name.localeCompare(b.name);\n        case \"type\":\n          return a.type.localeCompare(b.type);\n        case \"created\":\n          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();\n        default:\n          return 0;\n      }\n    });\n\n  if (rules.length === 0) {\n    return (\n      <Card>\n        <CardContent className=\"flex flex-col items-center justify-center py-12\">\n          <div className=\"text-6xl mb-4\">⚙️</div>\n          <h3 className=\"text-lg font-semibold mb-2\">还没有规则</h3>\n          <p className=\"text-gray-600 text-center mb-4\">\n            创建您的第一个规则，或从模板导入规则开始使用\n          </p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 过滤和搜索 */}\n      <Card>\n        <CardHeader>\n          <CardTitle>筛选和搜索</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">搜索</label>\n              <Input\n                placeholder=\"搜索规则名称、描述或标签...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n            \n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">规则类型</label>\n              <Select value={filterType} onValueChange={setFilterType}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"选择类型\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">所有类型</SelectItem>\n                  {Object.entries(ruleTypeLabels).map(([value, label]) => (\n                    <SelectItem key={value} value={value}>{label}</SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n            \n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">启用状态</label>\n              <Select value={filterEnabled} onValueChange={setFilterEnabled}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"选择状态\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">全部</SelectItem>\n                  <SelectItem value=\"enabled\">已启用</SelectItem>\n                  <SelectItem value=\"disabled\">已禁用</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            \n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">排序方式</label>\n              <Select value={sortBy} onValueChange={setSortBy}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"选择排序\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"priority\">优先级</SelectItem>\n                  <SelectItem value=\"name\">名称</SelectItem>\n                  <SelectItem value=\"type\">类型</SelectItem>\n                  <SelectItem value=\"created\">创建时间</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 规则统计 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"text-2xl font-bold text-blue-600\">{rules.length}</div>\n            <div className=\"text-sm text-gray-600\">总规则数</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"text-2xl font-bold text-green-600\">\n              {rules.filter(r => r.enabled).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">已启用</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"text-2xl font-bold text-orange-600\">\n              {rules.filter(r => !r.enabled).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">已禁用</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"text-2xl font-bold text-purple-600\">\n              {filteredAndSortedRules.length}\n            </div>\n            <div className=\"text-sm text-gray-600\">筛选结果</div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* 规则列表 */}\n      <div className=\"space-y-4\">\n        {filteredAndSortedRules.map((rule) => (\n          <Card key={rule.id} className={`transition-all hover:shadow-md ${!rule.enabled ? 'opacity-60' : ''}`}>\n            <CardHeader>\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <CardTitle className=\"text-lg\">{rule.name}</CardTitle>\n                    <Badge \n                      variant=\"secondary\" \n                      className={ruleTypeColors[rule.type] || \"bg-gray-100 text-gray-800\"}\n                    >\n                      {ruleTypeLabels[rule.type] || rule.type}\n                    </Badge>\n                    <Badge variant=\"outline\">\n                      优先级: {rule.priority}\n                    </Badge>\n                  </div>\n                  <CardDescription className=\"text-sm\">\n                    {rule.description}\n                  </CardDescription>\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <Switch\n                    checked={rule.enabled}\n                    onCheckedChange={(checked) => onToggleEnabled(rule.id, checked)}\n                  />\n                  <span className=\"text-sm text-gray-600\">\n                    {rule.enabled ? \"启用\" : \"禁用\"}\n                  </span>\n                </div>\n              </div>\n            </CardHeader>\n            \n            <CardContent>\n              <div className=\"space-y-3\">\n                {/* 规则详情 */}\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                  <div>\n                    <span className=\"font-medium text-gray-700\">条件数量:</span>\n                    <span className=\"ml-2\">{rule.conditions.length}</span>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-700\">动作数量:</span>\n                    <span className=\"ml-2\">{rule.actions.length}</span>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-700\">标签:</span>\n                    <span className=\"ml-2\">\n                      {rule.tags.length > 0 ? rule.tags.join(\", \") : \"无\"}\n                    </span>\n                  </div>\n                </div>\n\n                {/* 标签 */}\n                {rule.tags.length > 0 && (\n                  <div className=\"flex flex-wrap gap-2\">\n                    {rule.tags.map((tag, index) => (\n                      <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                        {tag}\n                      </Badge>\n                    ))}\n                  </div>\n                )}\n\n                {/* 操作按钮 */}\n                <div className=\"flex space-x-2 pt-2 border-t\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => onEdit(rule)}\n                  >\n                    ✏️ 编辑\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => {\n                      // 复制规则\n                      const newRule = {\n                        ...rule,\n                        id: `rule_${Date.now()}`,\n                        name: `${rule.name} (副本)`,\n                      };\n                      onEdit(newRule);\n                    }}\n                  >\n                    📋 复制\n                  </Button>\n                  <Button\n                    variant=\"destructive\"\n                    size=\"sm\"\n                    onClick={() => onDelete(rule.id)}\n                  >\n                    🗑️ 删除\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {filteredAndSortedRules.length === 0 && rules.length > 0 && (\n        <Card>\n          <CardContent className=\"flex flex-col items-center justify-center py-12\">\n            <div className=\"text-4xl mb-4\">🔍</div>\n            <h3 className=\"text-lg font-semibold mb-2\">没有找到匹配的规则</h3>\n            <p className=\"text-gray-600 text-center\">\n              尝试调整搜索条件或筛选器\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AA+BA,MAAM,iBAAyC;IAC7C,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,eAAe;IACf,aAAa;AACf;AAEA,MAAM,iBAAyC;IAC7C,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,eAAe;IACf,aAAa;AACf;AAEe,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAiB;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,UAAU;IACV,MAAM,yBAAyB,MAC5B,MAAM,CAAC,CAAA;QACN,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE5F,MAAM,cAAc,eAAe,SAAS,KAAK,IAAI,KAAK;QAC1D,MAAM,iBAAiB,kBAAkB,SAClB,kBAAkB,aAAa,KAAK,OAAO,IAC3C,kBAAkB,cAAc,CAAC,KAAK,OAAO;QAEpE,OAAO,iBAAiB,eAAe;IACzC,GACC,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,UAAU,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,IAAI,GAAG,OAAO;YACpF;gBACE,OAAO;QACX;IACF;IAEF,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAAiC;;;;;;;;;;;;;;;;;IAMtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAIjD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAY,eAAe;;8DACxC,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACjD,8OAAC,kIAAA,CAAA,aAAU;gEAAa,OAAO;0EAAQ;+DAAtB;;;;;;;;;;;;;;;;;;;;;;;8CAMzB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAe,eAAe;;8DAC3C,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;;;;;;;;;;;;;;;;;;;8CAKnC,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAQ,eAAe;;8DACpC,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CAAoC,MAAM,MAAM;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAG3C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;;;;;;8CAEtC,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAG3C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACZ,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO,EAAE,MAAM;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAG3C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACZ,uBAAuB,MAAM;;;;;;8CAEhC,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC;gBAAI,WAAU;0BACZ,uBAAuB,GAAG,CAAC,CAAC,qBAC3B,8OAAC,gIAAA,CAAA,OAAI;wBAAe,WAAW,CAAC,+BAA+B,EAAE,CAAC,KAAK,OAAO,GAAG,eAAe,IAAI;;0CAClG,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAW,KAAK,IAAI;;;;;;sEACzC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,cAAc,CAAC,KAAK,IAAI,CAAC,IAAI;sEAEvC,cAAc,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI;;;;;;sEAEzC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;;gEAAU;gEACjB,KAAK,QAAQ;;;;;;;;;;;;;8DAGvB,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,KAAK,WAAW;;;;;;;;;;;;sDAIrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,KAAK,OAAO;oDACrB,iBAAiB,CAAC,UAAY,gBAAgB,KAAK,EAAE,EAAE;;;;;;8DAEzD,8OAAC;oDAAK,WAAU;8DACb,KAAK,OAAO,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;0CAM/B,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAK,WAAU;sEAAQ,KAAK,UAAU,CAAC,MAAM;;;;;;;;;;;;8DAEhD,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAK,WAAU;sEAAQ,KAAK,OAAO,CAAC,MAAM;;;;;;;;;;;;8DAE7C,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAK,WAAU;sEACb,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ;;;;;;;;;;;;;;;;;;wCAMpD,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,iIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAU,WAAU;8DAC5C;mDADS;;;;;;;;;;sDAQlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,OAAO;8DACvB;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,OAAO;wDACP,MAAM,UAAU;4DACd,GAAG,IAAI;4DACP,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;4DACxB,MAAM,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;wDAC3B;wDACA,OAAO;oDACT;8DACD;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,SAAS,KAAK,EAAE;8DAChC;;;;;;;;;;;;;;;;;;;;;;;;uBA5FE,KAAK,EAAE;;;;;;;;;;YAsGrB,uBAAuB,MAAM,KAAK,KAAK,MAAM,MAAM,GAAG,mBACrD,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/rules/RuleTemplates.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Input } from \"@/components/ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\n\ninterface Rule {\n  id: string;\n  name: string;\n  description: string;\n  type: string;\n  enabled: boolean;\n  priority: number;\n  conditions: any[];\n  actions: any[];\n  tags: string[];\n}\n\ninterface RuleTemplate {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  rule: Rule;\n  usage_count: number;\n  is_builtin: boolean;\n}\n\ninterface RuleTemplatesProps {\n  templates: RuleTemplate[];\n  onCreateFromTemplate: (templateId: string) => void;\n}\n\nconst categoryLabels: Record<string, string> = {\n  \"内容过滤\": \"🔍 内容过滤\",\n  \"格式调整\": \"📝 格式调整\",\n  \"内容清理\": \"🧹 内容清理\",\n  \"语言优化\": \"✨ 语言优化\"\n};\n\nconst categoryColors: Record<string, string> = {\n  \"内容过滤\": \"bg-blue-100 text-blue-800\",\n  \"格式调整\": \"bg-green-100 text-green-800\",\n  \"内容清理\": \"bg-yellow-100 text-yellow-800\",\n  \"语言优化\": \"bg-purple-100 text-purple-800\"\n};\n\nexport default function RuleTemplates({ templates, onCreateFromTemplate }: RuleTemplatesProps) {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedCategory, setSelectedCategory] = useState<string>(\"all\");\n\n  // 按分类组织模板\n  const templatesByCategory = templates.reduce((acc, template) => {\n    if (!acc[template.category]) {\n      acc[template.category] = [];\n    }\n    acc[template.category].push(template);\n    return acc;\n  }, {} as Record<string, RuleTemplate[]>);\n\n  // 过滤模板\n  const filteredTemplates = templates.filter(template => {\n    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         template.rule.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    \n    const matchesCategory = selectedCategory === \"all\" || template.category === selectedCategory;\n    \n    return matchesSearch && matchesCategory;\n  });\n\n  const categories = Object.keys(templatesByCategory);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 搜索和筛选 */}\n      <Card>\n        <CardHeader>\n          <CardTitle>模板筛选</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">搜索模板</label>\n              <Input\n                placeholder=\"搜索模板名称或描述...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n            \n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">模板分类</label>\n              <Select value={selectedCategory} onValueChange={setSelectedCategory}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"选择分类\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">所有分类</SelectItem>\n                  {categories.map((category) => (\n                    <SelectItem key={category} value={category}>\n                      {categoryLabels[category] || category}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 模板统计 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"text-2xl font-bold text-blue-600\">{templates.length}</div>\n            <div className=\"text-sm text-gray-600\">总模板数</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"text-2xl font-bold text-green-600\">\n              {templates.filter(t => t.is_builtin).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">内置模板</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"text-2xl font-bold text-purple-600\">{categories.length}</div>\n            <div className=\"text-sm text-gray-600\">分类数量</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"text-2xl font-bold text-orange-600\">\n              {filteredTemplates.length}\n            </div>\n            <div className=\"text-sm text-gray-600\">筛选结果</div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* 模板展示 */}\n      <Tabs defaultValue=\"grid\" className=\"space-y-4\">\n        <TabsList>\n          <TabsTrigger value=\"grid\">网格视图</TabsTrigger>\n          <TabsTrigger value=\"category\">分类视图</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"grid\" className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {filteredTemplates.map((template) => (\n              <TemplateCard\n                key={template.id}\n                template={template}\n                onCreateFromTemplate={onCreateFromTemplate}\n              />\n            ))}\n          </div>\n        </TabsContent>\n\n        <TabsContent value=\"category\" className=\"space-y-6\">\n          {categories.map((category) => {\n            const categoryTemplates = templatesByCategory[category].filter(template =>\n              selectedCategory === \"all\" || template.category === selectedCategory\n            ).filter(template =>\n              template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n              template.description.toLowerCase().includes(searchTerm.toLowerCase())\n            );\n\n            if (categoryTemplates.length === 0) return null;\n\n            return (\n              <div key={category}>\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <h3 className=\"text-lg font-semibold\">\n                    {categoryLabels[category] || category}\n                  </h3>\n                  <Badge variant=\"outline\">\n                    {categoryTemplates.length} 个模板\n                  </Badge>\n                </div>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {categoryTemplates.map((template) => (\n                    <TemplateCard\n                      key={template.id}\n                      template={template}\n                      onCreateFromTemplate={onCreateFromTemplate}\n                    />\n                  ))}\n                </div>\n              </div>\n            );\n          })}\n        </TabsContent>\n      </Tabs>\n\n      {filteredTemplates.length === 0 && (\n        <Card>\n          <CardContent className=\"flex flex-col items-center justify-center py-12\">\n            <div className=\"text-4xl mb-4\">📋</div>\n            <h3 className=\"text-lg font-semibold mb-2\">没有找到匹配的模板</h3>\n            <p className=\"text-gray-600 text-center\">\n              尝试调整搜索条件或选择不同的分类\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n\n// 模板卡片组件\nfunction TemplateCard({ \n  template, \n  onCreateFromTemplate \n}: { \n  template: RuleTemplate; \n  onCreateFromTemplate: (templateId: string) => void;\n}) {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  return (\n    <Card className=\"transition-all hover:shadow-md\">\n      <CardHeader>\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <CardTitle className=\"text-base mb-2\">{template.name}</CardTitle>\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Badge \n                variant=\"secondary\"\n                className={categoryColors[template.category] || \"bg-gray-100 text-gray-800\"}\n              >\n                {template.category}\n              </Badge>\n              {template.is_builtin && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  内置\n                </Badge>\n              )}\n            </div>\n          </div>\n        </div>\n        <CardDescription className=\"text-sm\">\n          {template.description}\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent>\n        <div className=\"space-y-3\">\n          {/* 规则详情 */}\n          <div className=\"text-sm space-y-1\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">规则类型:</span>\n              <span className=\"font-medium\">{template.rule.type}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">优先级:</span>\n              <span className=\"font-medium\">{template.rule.priority}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">条件数:</span>\n              <span className=\"font-medium\">{template.rule.conditions.length}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">动作数:</span>\n              <span className=\"font-medium\">{template.rule.actions.length}</span>\n            </div>\n          </div>\n\n          {/* 标签 */}\n          {template.rule.tags.length > 0 && (\n            <div className=\"flex flex-wrap gap-1\">\n              {template.rule.tags.map((tag, index) => (\n                <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                  {tag}\n                </Badge>\n              ))}\n            </div>\n          )}\n\n          {/* 详细信息 */}\n          {isExpanded && (\n            <div className=\"border-t pt-3 space-y-2\">\n              <div className=\"text-sm\">\n                <div className=\"font-medium mb-1\">条件:</div>\n                {template.rule.conditions.length > 0 ? (\n                  <ul className=\"list-disc list-inside text-gray-600 space-y-1\">\n                    {template.rule.conditions.map((condition, index) => (\n                      <li key={index} className=\"text-xs\">\n                        {condition.field} {condition.operator} \"{condition.value}\"\n                      </li>\n                    ))}\n                  </ul>\n                ) : (\n                  <p className=\"text-gray-500 text-xs\">无条件限制</p>\n                )}\n              </div>\n              \n              <div className=\"text-sm\">\n                <div className=\"font-medium mb-1\">动作:</div>\n                <ul className=\"list-disc list-inside text-gray-600 space-y-1\">\n                  {template.rule.actions.map((action, index) => (\n                    <li key={index} className=\"text-xs\">\n                      {action.type}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          )}\n\n          {/* 操作按钮 */}\n          <div className=\"flex space-x-2 pt-2 border-t\">\n            <Button\n              size=\"sm\"\n              onClick={() => onCreateFromTemplate(template.id)}\n              className=\"flex-1\"\n            >\n              ➕ 使用模板\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setIsExpanded(!isExpanded)}\n            >\n              {isExpanded ? \"收起\" : \"详情\"}\n            </Button>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAqCA,MAAM,iBAAyC;IAC7C,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;AACV;AAEA,MAAM,iBAAyC;IAC7C,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;AACV;AAEe,SAAS,cAAc,EAAE,SAAS,EAAE,oBAAoB,EAAsB;IAC3F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,UAAU;IACV,MAAM,sBAAsB,UAAU,MAAM,CAAC,CAAC,KAAK;QACjD,IAAI,CAAC,GAAG,CAAC,SAAS,QAAQ,CAAC,EAAE;YAC3B,GAAG,CAAC,SAAS,QAAQ,CAAC,GAAG,EAAE;QAC7B;QACA,GAAG,CAAC,SAAS,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC5B,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO;IACP,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAErG,MAAM,kBAAkB,qBAAqB,SAAS,SAAS,QAAQ,KAAK;QAE5E,OAAO,iBAAiB;IAC1B;IAEA,MAAM,aAAa,OAAO,IAAI,CAAC;IAE/B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAIjD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAkB,eAAe;;8DAC9C,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,aAAU;gEAAgB,OAAO;0EAC/B,cAAc,CAAC,SAAS,IAAI;+DADd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CAAoC,UAAU,MAAM;;;;;;8CACnE,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAG3C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;8CAE7C,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAG3C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CAAsC,WAAW,MAAM;;;;;;8CACtE,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAG3C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,MAAM;;;;;;8CAE3B,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAO,WAAU;;kCAClC,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;0CAC1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;;;;;;;kCAGhC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;kCAClC,cAAA,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;oCAEC,UAAU;oCACV,sBAAsB;mCAFjB,SAAS,EAAE;;;;;;;;;;;;;;;kCAQxB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACrC,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,oBAAoB,mBAAmB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,WAC7D,qBAAqB,SAAS,SAAS,QAAQ,KAAK,kBACpD,MAAM,CAAC,CAAA,WACP,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;4BAGpE,IAAI,kBAAkB,MAAM,KAAK,GAAG,OAAO;4BAE3C,qBACE,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,cAAc,CAAC,SAAS,IAAI;;;;;;0DAE/B,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;;oDACZ,kBAAkB,MAAM;oDAAC;;;;;;;;;;;;;kDAI9B,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;gDAEC,UAAU;gDACV,sBAAsB;+CAFjB,SAAS,EAAE;;;;;;;;;;;+BAbd;;;;;wBAqBd;;;;;;;;;;;;YAIH,kBAAkB,MAAM,KAAK,mBAC5B,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;;;;;;;;;;;;;AAQrD;AAEA,SAAS;AACT,SAAS,aAAa,EACpB,QAAQ,EACR,oBAAoB,EAIrB;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAkB,SAAS,IAAI;;;;;;8CACpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAW,cAAc,CAAC,SAAS,QAAQ,CAAC,IAAI;sDAE/C,SAAS,QAAQ;;;;;;wCAEnB,SAAS,UAAU,kBAClB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCACxB,SAAS,WAAW;;;;;;;;;;;;0BAIzB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI,CAAC,IAAI;;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI,CAAC,QAAQ;;;;;;;;;;;;8CAEvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI,CAAC,UAAU,CAAC,MAAM;;;;;;;;;;;;8CAEhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;;wBAK9D,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,mBAC3B,8OAAC;4BAAI,WAAU;sCACZ,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC5B,8OAAC,iIAAA,CAAA,QAAK;oCAAa,SAAQ;oCAAU,WAAU;8CAC5C;mCADS;;;;;;;;;;wBAQjB,4BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmB;;;;;;wCACjC,SAAS,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,kBACjC,8OAAC;4CAAG,WAAU;sDACX,SAAS,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACxC,8OAAC;oDAAe,WAAU;;wDACvB,UAAU,KAAK;wDAAC;wDAAE,UAAU,QAAQ;wDAAC;wDAAG,UAAU,KAAK;wDAAC;;mDADlD;;;;;;;;;iEAMb,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmB;;;;;;sDAClC,8OAAC;4CAAG,WAAU;sDACX,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAClC,8OAAC;oDAAe,WAAU;8DACvB,OAAO,IAAI;mDADL;;;;;;;;;;;;;;;;;;;;;;sCAUnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,IAAM,qBAAqB,SAAS,EAAE;oCAC/C,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,cAAc,CAAC;8CAE7B,aAAa,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC", "debugId": null}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/rules/RulePreview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\n\ninterface Rule {\n  id: string;\n  name: string;\n  description: string;\n  type: string;\n  enabled: boolean;\n  priority: number;\n  conditions: any[];\n  actions: any[];\n  tags: string[];\n}\n\ninterface RulePreviewProps {\n  rule: Rule;\n}\n\nconst defaultTestText = `M：主任您好，华为委托的第三方。\n1：好，您说。\nM：想了解一下我们跟华为合作的使用它的产品方案。\n1：嗯，得有一二十年了，基本上用了它网络安全什么服务器什么一类的。\nM：那您觉得华为的产品质量怎么样？\n1：啊，我觉得还是不错的，技术比较先进。`;\n\nexport default function RulePreview({ rule }: RulePreviewProps) {\n  const [testText, setTestText] = useState(defaultTestText);\n  const [result, setResult] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const testRule = async () => {\n    if (!testText.trim()) {\n      setError(\"请输入测试文本\");\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/rules/test\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        mode: 'cors',\n        body: JSON.stringify({\n          rule: rule,\n          test_text: testText\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`测试失败: ${response.status}`);\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"测试过程中发生错误\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetTest = () => {\n    setTestText(defaultTestText);\n    setResult(null);\n    setError(null);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>🔍 规则预览测试</CardTitle>\n          <CardDescription>\n            测试规则效果，查看转换结果和执行统计\n          </CardDescription>\n        </CardHeader>\n      </Card>\n\n      {/* 规则信息概览 */}\n      <Card>\n        <CardHeader>\n          <CardTitle>规则信息</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <div>\n              <Label className=\"text-sm font-medium text-gray-600\">规则名称</Label>\n              <p className=\"font-semibold\">{rule.name}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium text-gray-600\">规则类型</Label>\n              <p className=\"font-semibold\">{rule.type}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium text-gray-600\">优先级</Label>\n              <p className=\"font-semibold\">{rule.priority}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium text-gray-600\">状态</Label>\n              <Badge variant={rule.enabled ? \"default\" : \"secondary\"}>\n                {rule.enabled ? \"启用\" : \"禁用\"}\n              </Badge>\n            </div>\n          </div>\n\n          <div className=\"mt-4\">\n            <Label className=\"text-sm font-medium text-gray-600\">描述</Label>\n            <p className=\"text-sm text-gray-700\">{rule.description}</p>\n          </div>\n\n          {rule.tags.length > 0 && (\n            <div className=\"mt-4\">\n              <Label className=\"text-sm font-medium text-gray-600\">标签</Label>\n              <div className=\"flex flex-wrap gap-2 mt-1\">\n                {rule.tags.map((tag, index) => (\n                  <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                    {tag}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n            <div>\n              <Label className=\"text-sm font-medium text-gray-600\">条件数量</Label>\n              <p className=\"text-sm\">{rule.conditions.length} 个条件</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium text-gray-600\">动作数量</Label>\n              <p className=\"text-sm\">{rule.actions.length} 个动作</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Tabs defaultValue=\"test\" className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-3\">\n          <TabsTrigger value=\"test\">测试规则</TabsTrigger>\n          <TabsTrigger value=\"conditions\">条件详情</TabsTrigger>\n          <TabsTrigger value=\"actions\">动作详情</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"test\" className=\"space-y-6\">\n          {/* 测试输入 */}\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <CardTitle>测试文本</CardTitle>\n                  <CardDescription>\n                    输入要测试的文本内容\n                  </CardDescription>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <Button variant=\"outline\" onClick={resetTest}>\n                    🔄 重置\n                  </Button>\n                  <Button onClick={testRule} disabled={loading}>\n                    {loading ? \"测试中...\" : \"🧪 测试规则\"}\n                  </Button>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent>\n              <Textarea\n                value={testText}\n                onChange={(e) => setTestText(e.target.value)}\n                placeholder=\"输入要测试的文本...\"\n                className=\"min-h-[200px] resize-none\"\n              />\n              <div className=\"text-sm text-gray-500 mt-2\">\n                字符数: {testText.length}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 错误显示 */}\n          {error && (\n            <Card className=\"border-red-200 bg-red-50\">\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-red-600\">❌</span>\n                  <span className=\"text-red-700 font-medium\">测试失败</span>\n                </div>\n                <p className=\"text-red-600 text-sm mt-1\">{error}</p>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* 测试结果 */}\n          {result && (\n            <div className=\"space-y-4\">\n              {/* 执行统计 */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>执行统计</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-blue-600\">\n                        {result.success ? \"✅\" : \"❌\"}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">执行状态</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-green-600\">\n                        {result.rules_applied?.length || 0}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">规则应用</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-purple-600\">\n                        {result.total_execution_time_ms?.toFixed(2) || 0}ms\n                      </div>\n                      <div className=\"text-sm text-gray-600\">执行时间</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-orange-600\">\n                        {result.transformed_text?.length || 0}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">结果字符数</div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* 转换结果对比 */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>转换结果对比</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n                    <div>\n                      <Label className=\"text-sm font-medium text-gray-600 mb-2 block\">\n                        原始文本\n                      </Label>\n                      <div className=\"p-3 bg-gray-50 border rounded-md min-h-[200px] whitespace-pre-wrap text-sm\">\n                        {result.original_text}\n                      </div>\n                    </div>\n                    <div>\n                      <Label className=\"text-sm font-medium text-gray-600 mb-2 block\">\n                        转换结果\n                      </Label>\n                      <div className=\"p-3 bg-blue-50 border rounded-md min-h-[200px] whitespace-pre-wrap text-sm\">\n                        {result.transformed_text}\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* 规则执行详情 */}\n              {result.rules_applied && result.rules_applied.length > 0 && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle>规则执行详情</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      {result.rules_applied.map((ruleResult: any, index: number) => (\n                        <div key={index} className=\"p-3 border rounded-md\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <div className=\"font-medium\">{ruleResult.rule_name}</div>\n                            <Badge variant={ruleResult.applied ? \"default\" : \"secondary\"}>\n                              {ruleResult.applied ? \"已应用\" : \"未应用\"}\n                            </Badge>\n                          </div>\n                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                            <div>\n                              <span className=\"text-gray-600\">匹配次数:</span>\n                              <span className=\"ml-2 font-medium\">{ruleResult.matches_count}</span>\n                            </div>\n                            <div>\n                              <span className=\"text-gray-600\">修改次数:</span>\n                              <span className=\"ml-2 font-medium\">{ruleResult.changes_made}</span>\n                            </div>\n                            <div>\n                              <span className=\"text-gray-600\">执行时间:</span>\n                              <span className=\"ml-2 font-medium\">{ruleResult.execution_time_ms?.toFixed(2)}ms</span>\n                            </div>\n                          </div>\n                          {ruleResult.error_message && (\n                            <div className=\"mt-2 text-red-600 text-sm\">\n                              错误: {ruleResult.error_message}\n                            </div>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"conditions\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>条件详情</CardTitle>\n              <CardDescription>\n                规则的触发条件配置\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {rule.conditions.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🎯</div>\n                  <p>没有设置条件，规则将应用于所有文本</p>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {rule.conditions.map((condition, index) => (\n                    <div key={index} className=\"p-3 border rounded-md\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm\">\n                        <div>\n                          <span className=\"text-gray-600\">字段:</span>\n                          <span className=\"ml-2 font-medium\">{condition.field}</span>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">操作符:</span>\n                          <span className=\"ml-2 font-medium\">{condition.operator}</span>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">值:</span>\n                          <span className=\"ml-2 font-medium\">\"{condition.value}\"</span>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">大小写敏感:</span>\n                          <span className=\"ml-2 font-medium\">\n                            {condition.case_sensitive ? \"是\" : \"否\"}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"actions\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>动作详情</CardTitle>\n              <CardDescription>\n                满足条件时执行的动作配置\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {rule.actions.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">⚡</div>\n                  <p>没有设置动作，规则不会产生任何效果</p>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {rule.actions.map((action, index) => (\n                    <div key={index} className=\"p-3 border rounded-md\">\n                      <div className=\"mb-2\">\n                        <span className=\"text-gray-600\">动作类型:</span>\n                        <span className=\"ml-2 font-medium\">{action.type}</span>\n                      </div>\n                      {Object.keys(action.params).length > 0 && (\n                        <div className=\"space-y-1\">\n                          <div className=\"text-sm text-gray-600\">参数:</div>\n                          <div className=\"pl-4 space-y-1\">\n                            {Object.entries(action.params).map(([key, value]) => (\n                              <div key={key} className=\"text-sm\">\n                                <span className=\"text-gray-600\">{key}:</span>\n                                <span className=\"ml-2 font-medium\">\n                                  {typeof value === 'boolean' ? (value ? '是' : '否') : String(value)}\n                                </span>\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AA0BA,MAAM,kBAAkB,CAAC;;;;;oBAKL,CAAC;AAEN,SAAS,YAAY,EAAE,IAAI,EAAoB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,WAAW;QACf,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2CAA2C;gBACtE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM;gBACN,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,SAAS,MAAM,EAAE;YAC5C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;QACZ,UAAU;QACV,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;sCACX,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;;;;;;0BAOrB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAE,WAAU;0DAAiB,KAAK,IAAI;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAE,WAAU;0DAAiB,KAAK,IAAI;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAE,WAAU;0DAAiB,KAAK,QAAQ;;;;;;;;;;;;kDAE7C,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,KAAK,OAAO,GAAG,YAAY;0DACxC,KAAK,OAAO,GAAG,OAAO;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAAyB,KAAK,WAAW;;;;;;;;;;;;4BAGvD,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,iIAAA,CAAA,QAAK;gDAAa,SAAQ;gDAAU,WAAU;0DAC5C;+CADS;;;;;;;;;;;;;;;;0CAQpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAE,WAAU;;oDAAW,KAAK,UAAU,CAAC,MAAM;oDAAC;;;;;;;;;;;;;kDAEjD,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAE,WAAU;;oDAAW,KAAK,OAAO,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMpD,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAO,WAAU;;kCAClC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;0CAC1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAa;;;;;;0CAChC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;;;;;;;kCAG/B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;;0CAElC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,SAAS;sEAAW;;;;;;sEAG9C,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS;4DAAU,UAAU;sEAClC,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAK9B,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC,oIAAA,CAAA,WAAQ;gDACP,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;oDAA6B;oDACpC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;4BAM1B,uBACC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAe;;;;;;8DAC/B,8OAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;sDAE7C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;4BAM/C,wBACC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;0DAEb,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,OAAO,OAAO,GAAG,MAAM;;;;;;8EAE1B,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,OAAO,aAAa,EAAE,UAAU;;;;;;8EAEnC,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEACZ,OAAO,uBAAuB,EAAE,QAAQ,MAAM;wEAAE;;;;;;;8EAEnD,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,OAAO,gBAAgB,EAAE,UAAU;;;;;;8EAEtC,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO/C,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;0DAEb,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEAAI,WAAU;8EACZ,OAAO,aAAa;;;;;;;;;;;;sEAGzB,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEAAI,WAAU;8EACZ,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQjC,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,MAAM,GAAG,mBACrD,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;0DAEb,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;8DACZ,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,YAAiB,sBAC1C,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAe,WAAW,SAAS;;;;;;sFAClD,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAS,WAAW,OAAO,GAAG,YAAY;sFAC9C,WAAW,OAAO,GAAG,QAAQ;;;;;;;;;;;;8EAGlC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,8OAAC;oFAAK,WAAU;8FAAoB,WAAW,aAAa;;;;;;;;;;;;sFAE9D,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,8OAAC;oFAAK,WAAU;8FAAoB,WAAW,YAAY;;;;;;;;;;;;sFAE7D,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,8OAAC;oFAAK,WAAU;;wFAAoB,WAAW,iBAAiB,EAAE,QAAQ;wFAAG;;;;;;;;;;;;;;;;;;;gEAGhF,WAAW,aAAa,kBACvB,8OAAC;oEAAI,WAAU;;wEAA4B;wEACpC,WAAW,aAAa;;;;;;;;2DAvBzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAoC1B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAa,WAAU;kCACxC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACT,KAAK,UAAU,CAAC,MAAM,KAAK,kBAC1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;0DAAE;;;;;;;;;;;6DAGL,8OAAC;wCAAI,WAAU;kDACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC/B,8OAAC;gDAAgB,WAAU;0DACzB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAoB,UAAU,KAAK;;;;;;;;;;;;sEAErD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAoB,UAAU,QAAQ;;;;;;;;;;;;sEAExD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;;wEAAmB;wEAAE,UAAU,KAAK;wEAAC;;;;;;;;;;;;;sEAEvD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EACb,UAAU,cAAc,GAAG,MAAM;;;;;;;;;;;;;;;;;;+CAjBhC;;;;;;;;;;;;;;;;;;;;;;;;;;kCA6BtB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACT,KAAK,OAAO,CAAC,MAAM,KAAK,kBACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;0DAAE;;;;;;;;;;;6DAGL,8OAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzB,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAoB,OAAO,IAAI;;;;;;;;;;;;oDAEhD,OAAO,IAAI,CAAC,OAAO,MAAM,EAAE,MAAM,GAAG,mBACnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,8OAAC;gEAAI,WAAU;0EACZ,OAAO,OAAO,CAAC,OAAO,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC9C,8OAAC;wEAAc,WAAU;;0FACvB,8OAAC;gFAAK,WAAU;;oFAAiB;oFAAI;;;;;;;0FACrC,8OAAC;gFAAK,WAAU;0FACb,OAAO,UAAU,YAAa,QAAQ,MAAM,MAAO,OAAO;;;;;;;uEAHrD;;;;;;;;;;;;;;;;;+CAVV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8B9B", "debugId": null}}, {"offset": {"line": 3567, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/rules/RuleEditor.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport RulePreview from \"./RulePreview\";\n\ninterface Rule {\n  id: string;\n  name: string;\n  description: string;\n  type: string;\n  enabled: boolean;\n  priority: number;\n  conditions: RuleCondition[];\n  actions: RuleAction[];\n  tags: string[];\n  created_at?: string;\n  updated_at?: string;\n}\n\ninterface RuleCondition {\n  field: string;\n  operator: string;\n  value: string | number;\n  case_sensitive?: boolean;\n}\n\ninterface RuleAction {\n  type: string;\n  params: Record<string, any>;\n}\n\ninterface RuleEditorProps {\n  rule: Rule;\n  isEditing: boolean;\n  onSave: (rule: Rule) => void;\n  onCancel: () => void;\n}\n\nconst ruleTypes = [\n  { value: \"text_replace\", label: \"文本替换\" },\n  { value: \"regex_replace\", label: \"正则替换\" },\n  { value: \"speaker_filter\", label: \"发言人过滤\" },\n  { value: \"line_filter\", label: \"行过滤\" },\n  { value: \"format_adjust\", label: \"格式调整\" },\n  { value: \"conditional\", label: \"条件规则\" }\n];\n\nconst conditionFields = [\n  { value: \"line\", label: \"整行内容\" },\n  { value: \"speaker\", label: \"发言人\" },\n  { value: \"content\", label: \"发言内容\" },\n  { value: \"length\", label: \"文本长度\" }\n];\n\nconst conditionOperators = [\n  { value: \"equals\", label: \"等于\" },\n  { value: \"not_equals\", label: \"不等于\" },\n  { value: \"contains\", label: \"包含\" },\n  { value: \"not_contains\", label: \"不包含\" },\n  { value: \"starts_with\", label: \"开始于\" },\n  { value: \"ends_with\", label: \"结束于\" },\n  { value: \"regex_match\", label: \"正则匹配\" },\n  { value: \"length_gt\", label: \"长度大于\" },\n  { value: \"length_lt\", label: \"长度小于\" }\n];\n\nconst actionTypes = [\n  { value: \"remove_line\", label: \"删除行\" },\n  { value: \"replace_text\", label: \"替换文本\" },\n  { value: \"add_prefix\", label: \"添加前缀\" },\n  { value: \"add_suffix\", label: \"添加后缀\" },\n  { value: \"transform_case\", label: \"转换大小写\" },\n  { value: \"extract_content\", label: \"提取内容\" }\n];\n\nexport default function RuleEditor({ rule, isEditing, onSave, onCancel }: RuleEditorProps) {\n  const [editedRule, setEditedRule] = useState<Rule>(rule);\n  const [newTag, setNewTag] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"basic\");\n\n  useEffect(() => {\n    setEditedRule(rule);\n  }, [rule]);\n\n  const handleSave = () => {\n    onSave(editedRule);\n  };\n\n  const addCondition = () => {\n    const newCondition: RuleCondition = {\n      field: \"line\",\n      operator: \"contains\",\n      value: \"\",\n      case_sensitive: true\n    };\n    \n    setEditedRule(prev => ({\n      ...prev,\n      conditions: [...prev.conditions, newCondition]\n    }));\n  };\n\n  const updateCondition = (index: number, updates: Partial<RuleCondition>) => {\n    setEditedRule(prev => ({\n      ...prev,\n      conditions: prev.conditions.map((condition, i) => \n        i === index ? { ...condition, ...updates } : condition\n      )\n    }));\n  };\n\n  const removeCondition = (index: number) => {\n    setEditedRule(prev => ({\n      ...prev,\n      conditions: prev.conditions.filter((_, i) => i !== index)\n    }));\n  };\n\n  const addAction = () => {\n    const newAction: RuleAction = {\n      type: \"replace_text\",\n      params: {}\n    };\n    \n    setEditedRule(prev => ({\n      ...prev,\n      actions: [...prev.actions, newAction]\n    }));\n  };\n\n  const updateAction = (index: number, updates: Partial<RuleAction>) => {\n    setEditedRule(prev => ({\n      ...prev,\n      actions: prev.actions.map((action, i) => \n        i === index ? { ...action, ...updates } : action\n      )\n    }));\n  };\n\n  const removeAction = (index: number) => {\n    setEditedRule(prev => ({\n      ...prev,\n      actions: prev.actions.filter((_, i) => i !== index)\n    }));\n  };\n\n  const addTag = () => {\n    if (newTag.trim() && !editedRule.tags.includes(newTag.trim())) {\n      setEditedRule(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }));\n      setNewTag(\"\");\n    }\n  };\n\n  const removeTag = (tagToRemove: string) => {\n    setEditedRule(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>\n            {isEditing ? \"编辑规则\" : \"规则详情\"}: {editedRule.name}\n          </CardTitle>\n          <CardDescription>\n            {isEditing ? \"修改规则配置和参数\" : \"查看规则的详细配置\"}\n          </CardDescription>\n        </CardHeader>\n      </Card>\n\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-4\">\n          <TabsTrigger value=\"basic\">基本信息</TabsTrigger>\n          <TabsTrigger value=\"conditions\">条件设置</TabsTrigger>\n          <TabsTrigger value=\"actions\">动作设置</TabsTrigger>\n          <TabsTrigger value=\"preview\">预览测试</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"basic\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>基本信息</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"rule-name\">规则名称</Label>\n                  <Input\n                    id=\"rule-name\"\n                    value={editedRule.name}\n                    onChange={(e) => setEditedRule(prev => ({ ...prev, name: e.target.value }))}\n                    disabled={!isEditing}\n                  />\n                </div>\n                \n                <div>\n                  <Label htmlFor=\"rule-type\">规则类型</Label>\n                  <Select\n                    value={editedRule.type}\n                    onValueChange={(value) => setEditedRule(prev => ({ ...prev, type: value }))}\n                    disabled={!isEditing}\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {ruleTypes.map((type) => (\n                        <SelectItem key={type.value} value={type.value}>\n                          {type.label}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n\n              <div>\n                <Label htmlFor=\"rule-description\">规则描述</Label>\n                <Textarea\n                  id=\"rule-description\"\n                  value={editedRule.description}\n                  onChange={(e) => setEditedRule(prev => ({ ...prev, description: e.target.value }))}\n                  disabled={!isEditing}\n                  rows={3}\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"rule-priority\">优先级 (数字越小优先级越高)</Label>\n                  <Input\n                    id=\"rule-priority\"\n                    type=\"number\"\n                    min=\"1\"\n                    max=\"1000\"\n                    value={editedRule.priority}\n                    onChange={(e) => setEditedRule(prev => ({ ...prev, priority: parseInt(e.target.value) || 100 }))}\n                    disabled={!isEditing}\n                  />\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <Switch\n                    checked={editedRule.enabled}\n                    onCheckedChange={(checked) => setEditedRule(prev => ({ ...prev, enabled: checked }))}\n                    disabled={!isEditing}\n                  />\n                  <Label>启用规则</Label>\n                </div>\n              </div>\n\n              {/* 标签管理 */}\n              <div>\n                <Label>标签</Label>\n                <div className=\"flex flex-wrap gap-2 mb-2\">\n                  {editedRule.tags.map((tag) => (\n                    <Badge key={tag} variant=\"secondary\" className=\"flex items-center space-x-1\">\n                      <span>{tag}</span>\n                      {isEditing && (\n                        <button\n                          onClick={() => removeTag(tag)}\n                          className=\"ml-1 text-xs hover:text-red-600\"\n                        >\n                          ×\n                        </button>\n                      )}\n                    </Badge>\n                  ))}\n                </div>\n                {isEditing && (\n                  <div className=\"flex space-x-2\">\n                    <Input\n                      placeholder=\"添加标签\"\n                      value={newTag}\n                      onChange={(e) => setNewTag(e.target.value)}\n                      onKeyPress={(e) => e.key === 'Enter' && addTag()}\n                    />\n                    <Button onClick={addTag} variant=\"outline\">添加</Button>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"conditions\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <CardTitle>条件设置</CardTitle>\n                  <CardDescription>\n                    设置规则的触发条件，所有条件都必须满足\n                  </CardDescription>\n                </div>\n                {isEditing && (\n                  <Button onClick={addCondition} variant=\"outline\">\n                    ➕ 添加条件\n                  </Button>\n                )}\n              </div>\n            </CardHeader>\n            <CardContent>\n              {editedRule.conditions.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🎯</div>\n                  <p>没有设置条件，规则将应用于所有文本</p>\n                  {isEditing && (\n                    <Button onClick={addCondition} className=\"mt-4\">\n                      添加第一个条件\n                    </Button>\n                  )}\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {editedRule.conditions.map((condition, index) => (\n                    <Card key={index} className=\"border-l-4 border-l-blue-500\">\n                      <CardContent className=\"p-4\">\n                        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                          <div>\n                            <Label>字段</Label>\n                            <Select\n                              value={condition.field}\n                              onValueChange={(value) => updateCondition(index, { field: value })}\n                              disabled={!isEditing}\n                            >\n                              <SelectTrigger>\n                                <SelectValue />\n                              </SelectTrigger>\n                              <SelectContent>\n                                {conditionFields.map((field) => (\n                                  <SelectItem key={field.value} value={field.value}>\n                                    {field.label}\n                                  </SelectItem>\n                                ))}\n                              </SelectContent>\n                            </Select>\n                          </div>\n                          \n                          <div>\n                            <Label>操作符</Label>\n                            <Select\n                              value={condition.operator}\n                              onValueChange={(value) => updateCondition(index, { operator: value })}\n                              disabled={!isEditing}\n                            >\n                              <SelectTrigger>\n                                <SelectValue />\n                              </SelectTrigger>\n                              <SelectContent>\n                                {conditionOperators.map((op) => (\n                                  <SelectItem key={op.value} value={op.value}>\n                                    {op.label}\n                                  </SelectItem>\n                                ))}\n                              </SelectContent>\n                            </Select>\n                          </div>\n                          \n                          <div>\n                            <Label>值</Label>\n                            <Input\n                              value={condition.value}\n                              onChange={(e) => updateCondition(index, { value: e.target.value })}\n                              disabled={!isEditing}\n                              placeholder=\"输入比较值\"\n                            />\n                          </div>\n                          \n                          <div className=\"flex items-end space-x-2\">\n                            <div className=\"flex items-center space-x-2\">\n                              <Switch\n                                checked={condition.case_sensitive}\n                                onCheckedChange={(checked) => updateCondition(index, { case_sensitive: checked })}\n                                disabled={!isEditing}\n                              />\n                              <Label className=\"text-xs\">区分大小写</Label>\n                            </div>\n                            {isEditing && (\n                              <Button\n                                variant=\"destructive\"\n                                size=\"sm\"\n                                onClick={() => removeCondition(index)}\n                              >\n                                🗑️\n                              </Button>\n                            )}\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"actions\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <CardTitle>动作设置</CardTitle>\n                  <CardDescription>\n                    设置满足条件时要执行的动作\n                  </CardDescription>\n                </div>\n                {isEditing && (\n                  <Button onClick={addAction} variant=\"outline\">\n                    ➕ 添加动作\n                  </Button>\n                )}\n              </div>\n            </CardHeader>\n            <CardContent>\n              {editedRule.actions.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">⚡</div>\n                  <p>没有设置动作，规则不会产生任何效果</p>\n                  {isEditing && (\n                    <Button onClick={addAction} className=\"mt-4\">\n                      添加第一个动作\n                    </Button>\n                  )}\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {editedRule.actions.map((action, index) => (\n                    <ActionEditor\n                      key={index}\n                      action={action}\n                      isEditing={isEditing}\n                      onUpdate={(updates) => updateAction(index, updates)}\n                      onRemove={() => removeAction(index)}\n                    />\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"preview\" className=\"space-y-6\">\n          <RulePreview rule={editedRule} />\n        </TabsContent>\n      </Tabs>\n\n      {/* 操作按钮 */}\n      <div className=\"flex space-x-4\">\n        {isEditing && (\n          <Button onClick={handleSave} className=\"flex-1\">\n            💾 保存规则\n          </Button>\n        )}\n        <Button variant=\"outline\" onClick={onCancel} className=\"flex-1\">\n          {isEditing ? \"取消编辑\" : \"返回列表\"}\n        </Button>\n      </div>\n    </div>\n  );\n}\n\n// 动作编辑器组件\nfunction ActionEditor({ \n  action, \n  isEditing, \n  onUpdate, \n  onRemove \n}: {\n  action: RuleAction;\n  isEditing: boolean;\n  onUpdate: (updates: Partial<RuleAction>) => void;\n  onRemove: () => void;\n}) {\n  const updateParams = (key: string, value: any) => {\n    onUpdate({\n      params: { ...action.params, [key]: value }\n    });\n  };\n\n  return (\n    <Card className=\"border-l-4 border-l-green-500\">\n      <CardContent className=\"p-4\">\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex-1\">\n              <Label>动作类型</Label>\n              <Select\n                value={action.type}\n                onValueChange={(value) => onUpdate({ type: value, params: {} })}\n                disabled={!isEditing}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  {actionTypes.map((type) => (\n                    <SelectItem key={type.value} value={type.value}>\n                      {type.label}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n            {isEditing && (\n              <Button\n                variant=\"destructive\"\n                size=\"sm\"\n                onClick={onRemove}\n                className=\"ml-4\"\n              >\n                🗑️\n              </Button>\n            )}\n          </div>\n\n          {/* 根据动作类型显示不同的参数设置 */}\n          {action.type === \"replace_text\" && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label>查找模式</Label>\n                <Input\n                  value={action.params.pattern || \"\"}\n                  onChange={(e) => updateParams(\"pattern\", e.target.value)}\n                  disabled={!isEditing}\n                  placeholder=\"要替换的文本或正则表达式\"\n                />\n              </div>\n              <div>\n                <Label>替换为</Label>\n                <Input\n                  value={action.params.replacement || \"\"}\n                  onChange={(e) => updateParams(\"replacement\", e.target.value)}\n                  disabled={!isEditing}\n                  placeholder=\"替换后的文本\"\n                />\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  checked={action.params.use_regex || false}\n                  onCheckedChange={(checked) => updateParams(\"use_regex\", checked)}\n                  disabled={!isEditing}\n                />\n                <Label>使用正则表达式</Label>\n              </div>\n            </div>\n          )}\n\n          {(action.type === \"add_prefix\" || action.type === \"add_suffix\") && (\n            <div>\n              <Label>文本内容</Label>\n              <Input\n                value={action.params.text || \"\"}\n                onChange={(e) => updateParams(\"text\", e.target.value)}\n                disabled={!isEditing}\n                placeholder={`要${action.type === \"add_prefix\" ? \"添加到开头\" : \"添加到结尾\"}的文本`}\n              />\n            </div>\n          )}\n\n          {action.type === \"transform_case\" && (\n            <div>\n              <Label>大小写类型</Label>\n              <Select\n                value={action.params.case_type || \"lower\"}\n                onValueChange={(value) => updateParams(\"case_type\", value)}\n                disabled={!isEditing}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"upper\">大写</SelectItem>\n                  <SelectItem value=\"lower\">小写</SelectItem>\n                  <SelectItem value=\"title\">标题格式</SelectItem>\n                  <SelectItem value=\"capitalize\">首字母大写</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AA+CA,MAAM,YAAY;IAChB;QAAE,OAAO;QAAgB,OAAO;IAAO;IACvC;QAAE,OAAO;QAAiB,OAAO;IAAO;IACxC;QAAE,OAAO;QAAkB,OAAO;IAAQ;IAC1C;QAAE,OAAO;QAAe,OAAO;IAAM;IACrC;QAAE,OAAO;QAAiB,OAAO;IAAO;IACxC;QAAE,OAAO;QAAe,OAAO;IAAO;CACvC;AAED,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAW,OAAO;IAAM;IACjC;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAU,OAAO;IAAO;CAClC;AAED,MAAM,qBAAqB;IACzB;QAAE,OAAO;QAAU,OAAO;IAAK;IAC/B;QAAE,OAAO;QAAc,OAAO;IAAM;IACpC;QAAE,OAAO;QAAY,OAAO;IAAK;IACjC;QAAE,OAAO;QAAgB,OAAO;IAAM;IACtC;QAAE,OAAO;QAAe,OAAO;IAAM;IACrC;QAAE,OAAO;QAAa,OAAO;IAAM;IACnC;QAAE,OAAO;QAAe,OAAO;IAAO;IACtC;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAa,OAAO;IAAO;CACrC;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAe,OAAO;IAAM;IACrC;QAAE,OAAO;QAAgB,OAAO;IAAO;IACvC;QAAE,OAAO;QAAc,OAAO;IAAO;IACrC;QAAE,OAAO;QAAc,OAAO;IAAO;IACrC;QAAE,OAAO;QAAkB,OAAO;IAAQ;IAC1C;QAAE,OAAO;QAAmB,OAAO;IAAO;CAC3C;AAEc,SAAS,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAmB;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG;QAAC;KAAK;IAET,MAAM,aAAa;QACjB,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,MAAM,eAA8B;YAClC,OAAO;YACP,UAAU;YACV,OAAO;YACP,gBAAgB;QAClB;QAEA,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,YAAY;uBAAI,KAAK,UAAU;oBAAE;iBAAa;YAChD,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC,OAAe;QACtC,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,IAC1C,MAAM,QAAQ;wBAAE,GAAG,SAAS;wBAAE,GAAG,OAAO;oBAAC,IAAI;YAEjD,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACrD,CAAC;IACH;IAEA,MAAM,YAAY;QAChB,MAAM,YAAwB;YAC5B,MAAM;YACN,QAAQ,CAAC;QACX;QAEA,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,SAAS;uBAAI,KAAK,OAAO;oBAAE;iBAAU;YACvC,CAAC;IACH;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,SAAS,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,IACjC,MAAM,QAAQ;wBAAE,GAAG,MAAM;wBAAE,GAAG,OAAO;oBAAC,IAAI;YAE9C,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,SAAS,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC/C,CAAC;IACH;IAEA,MAAM,SAAS;QACb,IAAI,OAAO,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK;YAC7D,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,OAAO,IAAI;qBAAG;gBACrC,CAAC;YACD,UAAU;QACZ;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC,gIAAA,CAAA,YAAS;;gCACP,YAAY,SAAS;gCAAO;gCAAG,WAAW,IAAI;;;;;;;sCAEjD,8OAAC,gIAAA,CAAA,kBAAe;sCACb,YAAY,cAAc;;;;;;;;;;;;;;;;;0BAKjC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;gBAAc,WAAU;;kCAC7D,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAQ;;;;;;0CAC3B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAa;;;;;;0CAChC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;;;;;;;kCAG/B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAY;;;;;;sEAC3B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,WAAW,IAAI;4DACtB,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACzE,UAAU,CAAC;;;;;;;;;;;;8DAIf,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAY;;;;;;sEAC3B,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,WAAW,IAAI;4DACtB,eAAe,CAAC,QAAU,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM;oEAAM,CAAC;4DACzE,UAAU,CAAC;;8EAEX,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,8OAAC,kIAAA,CAAA,gBAAa;8EACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,kIAAA,CAAA,aAAU;4EAAkB,OAAO,KAAK,KAAK;sFAC3C,KAAK,KAAK;2EADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDASrC,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAmB;;;;;;8DAClC,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,WAAW,WAAW;oDAC7B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAChF,UAAU,CAAC;oDACX,MAAM;;;;;;;;;;;;sDAIV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAgB;;;;;;sEAC/B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,OAAO,WAAW,QAAQ;4DAC1B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAI,CAAC;4DAC9F,UAAU,CAAC;;;;;;;;;;;;8DAIf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,WAAW,OAAO;4DAC3B,iBAAiB,CAAC,UAAY,cAAc,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,SAAS;oEAAQ,CAAC;4DAClF,UAAU,CAAC;;;;;;sEAEb,8OAAC,iIAAA,CAAA,QAAK;sEAAC;;;;;;;;;;;;;;;;;;sDAKX,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,8OAAC;oDAAI,WAAU;8DACZ,WAAW,IAAI,CAAC,GAAG,CAAC,CAAC,oBACpB,8OAAC,iIAAA,CAAA,QAAK;4DAAW,SAAQ;4DAAY,WAAU;;8EAC7C,8OAAC;8EAAM;;;;;;gEACN,2BACC,8OAAC;oEACC,SAAS,IAAM,UAAU;oEACzB,WAAU;8EACX;;;;;;;2DANO;;;;;;;;;;gDAaf,2BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4DACzC,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;sEAE1C,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS;4DAAQ,SAAQ;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAa,WAAU;kCACxC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;4CAIlB,2BACC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAc,SAAQ;0DAAU;;;;;;;;;;;;;;;;;8CAMvD,8OAAC,gIAAA,CAAA,cAAW;8CACT,WAAW,UAAU,CAAC,MAAM,KAAK,kBAChC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;0DAAE;;;;;;4CACF,2BACC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAc,WAAU;0DAAO;;;;;;;;;;;6DAMpD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACrC,8OAAC,gIAAA,CAAA,OAAI;gDAAa,WAAU;0DAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,UAAU,KAAK;wEACtB,eAAe,CAAC,QAAU,gBAAgB,OAAO;gFAAE,OAAO;4EAAM;wEAChE,UAAU,CAAC;;0FAEX,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0FAEd,8OAAC,kIAAA,CAAA,gBAAa;0FACX,gBAAgB,GAAG,CAAC,CAAC,sBACpB,8OAAC,kIAAA,CAAA,aAAU;wFAAmB,OAAO,MAAM,KAAK;kGAC7C,MAAM,KAAK;uFADG,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;0EAQpC,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,UAAU,QAAQ;wEACzB,eAAe,CAAC,QAAU,gBAAgB,OAAO;gFAAE,UAAU;4EAAM;wEACnE,UAAU,CAAC;;0FAEX,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0FAEd,8OAAC,kIAAA,CAAA,gBAAa;0FACX,mBAAmB,GAAG,CAAC,CAAC,mBACvB,8OAAC,kIAAA,CAAA,aAAU;wFAAgB,OAAO,GAAG,KAAK;kGACvC,GAAG,KAAK;uFADM,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;;;0EAQjC,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC,iIAAA,CAAA,QAAK;wEACJ,OAAO,UAAU,KAAK;wEACtB,UAAU,CAAC,IAAM,gBAAgB,OAAO;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAChE,UAAU,CAAC;wEACX,aAAY;;;;;;;;;;;;0EAIhB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAS,UAAU,cAAc;gFACjC,iBAAiB,CAAC,UAAY,gBAAgB,OAAO;wFAAE,gBAAgB;oFAAQ;gFAC/E,UAAU,CAAC;;;;;;0FAEb,8OAAC,iIAAA,CAAA,QAAK;gFAAC,WAAU;0FAAU;;;;;;;;;;;;oEAE5B,2BACC,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,gBAAgB;kFAChC;;;;;;;;;;;;;;;;;;;;;;;+CAnEA;;;;;;;;;;;;;;;;;;;;;;;;;;kCAkFvB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;4CAIlB,2BACC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAW,SAAQ;0DAAU;;;;;;;;;;;;;;;;;8CAMpD,8OAAC,gIAAA,CAAA,cAAW;8CACT,WAAW,OAAO,CAAC,MAAM,KAAK,kBAC7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;0DAAE;;;;;;4CACF,2BACC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAW,WAAU;0DAAO;;;;;;;;;;;6DAMjD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC/B,8OAAC;gDAEC,QAAQ;gDACR,WAAW;gDACX,UAAU,CAAC,UAAY,aAAa,OAAO;gDAC3C,UAAU,IAAM,aAAa;+CAJxB;;;;;;;;;;;;;;;;;;;;;;;;;;kCAanB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,0IAAA,CAAA,UAAW;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;;oBACZ,2BACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,WAAU;kCAAS;;;;;;kCAIlD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAU,WAAU;kCACpD,YAAY,SAAS;;;;;;;;;;;;;;;;;;AAKhC;AAEA,UAAU;AACV,SAAS,aAAa,EACpB,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EAMT;IACC,MAAM,eAAe,CAAC,KAAa;QACjC,SAAS;YACP,QAAQ;gBAAE,GAAG,OAAO,MAAM;gBAAE,CAAC,IAAI,EAAE;YAAM;QAC3C;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,OAAO,IAAI;wCAClB,eAAe,CAAC,QAAU,SAAS;gDAAE,MAAM;gDAAO,QAAQ,CAAC;4CAAE;wCAC7D,UAAU,CAAC;;0DAEX,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;0DACX,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,kIAAA,CAAA,aAAU;wDAAkB,OAAO,KAAK,KAAK;kEAC3C,KAAK,KAAK;uDADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;4BAOlC,2BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;oBAOJ,OAAO,IAAI,KAAK,gCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI;wCAChC,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;wCACvD,UAAU,CAAC;wCACX,aAAY;;;;;;;;;;;;0CAGhB,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO,OAAO,MAAM,CAAC,WAAW,IAAI;wCACpC,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC3D,UAAU,CAAC;wCACX,aAAY;;;;;;;;;;;;0CAGhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,OAAO,MAAM,CAAC,SAAS,IAAI;wCACpC,iBAAiB,CAAC,UAAY,aAAa,aAAa;wCACxD,UAAU,CAAC;;;;;;kDAEb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;;;;;;;;;;;;;oBAKZ,CAAC,OAAO,IAAI,KAAK,gBAAgB,OAAO,IAAI,KAAK,YAAY,mBAC5D,8OAAC;;0CACC,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,iIAAA,CAAA,QAAK;gCACJ,OAAO,OAAO,MAAM,CAAC,IAAI,IAAI;gCAC7B,UAAU,CAAC,IAAM,aAAa,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACpD,UAAU,CAAC;gCACX,aAAa,CAAC,CAAC,EAAE,OAAO,IAAI,KAAK,eAAe,UAAU,QAAQ,GAAG,CAAC;;;;;;;;;;;;oBAK3E,OAAO,IAAI,KAAK,kCACf,8OAAC;;0CACC,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,OAAO,MAAM,CAAC,SAAS,IAAI;gCAClC,eAAe,CAAC,QAAU,aAAa,aAAa;gCACpD,UAAU,CAAC;;kDAEX,8OAAC,kIAAA,CAAA,gBAAa;kDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 4962, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/app/rules/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, Ta<PERSON>Trigger } from \"@/components/ui/tabs\";\nimport Link from \"next/link\";\nimport RuleList from \"@/components/rules/RuleList\";\nimport RuleTemplates from \"@/components/rules/RuleTemplates\";\nimport RuleEditor from \"@/components/rules/RuleEditor\";\n\ninterface Rule {\n  id: string;\n  name: string;\n  description: string;\n  type: string;\n  enabled: boolean;\n  priority: number;\n  conditions: any[];\n  actions: any[];\n  tags: string[];\n  created_at?: string;\n  updated_at?: string;\n}\n\ninterface RuleTemplate {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  rule: Rule;\n  usage_count: number;\n  is_builtin: boolean;\n}\n\nexport default function RulesPage() {\n  const [rules, setRules] = useState<Rule[]>([]);\n  const [templates, setTemplates] = useState<RuleTemplate[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedRule, setSelectedRule] = useState<Rule | null>(null);\n  const [isEditing, setIsEditing] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"list\");\n\n  // 加载规则列表\n  const loadRules = async () => {\n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/rules\", {\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`加载规则失败: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      setRules(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"加载规则失败\");\n    }\n  };\n\n  // 加载规则模板\n  const loadTemplates = async () => {\n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/rules/templates\", {\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`加载模板失败: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      setTemplates(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"加载模板失败\");\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      await Promise.all([loadRules(), loadTemplates()]);\n      setLoading(false);\n    };\n    \n    loadData();\n  }, []);\n\n  // 创建新规则\n  const handleCreateRule = () => {\n    const newRule: Rule = {\n      id: `rule_${Date.now()}`,\n      name: \"新规则\",\n      description: \"请输入规则描述\",\n      type: \"text_replace\",\n      enabled: true,\n      priority: 100,\n      conditions: [],\n      actions: [],\n      tags: []\n    };\n    \n    setSelectedRule(newRule);\n    setIsEditing(true);\n    setActiveTab(\"editor\");\n  };\n\n  // 编辑规则\n  const handleEditRule = (rule: Rule) => {\n    setSelectedRule(rule);\n    setIsEditing(true);\n    setActiveTab(\"editor\");\n  };\n\n  // 从模板创建规则\n  const handleCreateFromTemplate = async (templateId: string) => {\n    try {\n      const response = await fetch(`http://localhost:8002/api/v1/rules/import-template/${templateId}`, {\n        method: 'POST',\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`导入模板失败: ${response.status}`);\n      }\n      \n      const newRule = await response.json();\n      setRules(prev => [...prev, newRule]);\n      setSelectedRule(newRule);\n      setIsEditing(true);\n      setActiveTab(\"editor\");\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"导入模板失败\");\n    }\n  };\n\n  // 保存规则\n  const handleSaveRule = async (rule: Rule) => {\n    try {\n      const isNewRule = !rules.find(r => r.id === rule.id);\n      const url = isNewRule \n        ? \"http://localhost:8002/api/v1/rules\"\n        : `http://localhost:8002/api/v1/rules/${rule.id}`;\n      \n      const method = isNewRule ? 'POST' : 'PUT';\n      \n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        mode: 'cors',\n        body: JSON.stringify({ rule }),\n      });\n      \n      if (!response.ok) {\n        throw new Error(`保存规则失败: ${response.status}`);\n      }\n      \n      const savedRule = await response.json();\n      \n      if (isNewRule) {\n        setRules(prev => [...prev, savedRule]);\n      } else {\n        setRules(prev => prev.map(r => r.id === rule.id ? savedRule : r));\n      }\n      \n      setIsEditing(false);\n      setActiveTab(\"list\");\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"保存规则失败\");\n    }\n  };\n\n  // 删除规则\n  const handleDeleteRule = async (ruleId: string) => {\n    if (!confirm(\"确定要删除这个规则吗？\")) {\n      return;\n    }\n    \n    try {\n      const response = await fetch(`http://localhost:8002/api/v1/rules/${ruleId}`, {\n        method: 'DELETE',\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`删除规则失败: ${response.status}`);\n      }\n      \n      setRules(prev => prev.filter(r => r.id !== ruleId));\n      \n      if (selectedRule?.id === ruleId) {\n        setSelectedRule(null);\n        setIsEditing(false);\n        setActiveTab(\"list\");\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"删除规则失败\");\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-4xl mb-4\">⚙️</div>\n          <p className=\"text-gray-600\">加载规则管理器...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                📝 笔录转换系统\n              </Link>\n              <span className=\"ml-4 text-gray-500\">/</span>\n              <span className=\"ml-4 text-gray-700\">规则管理</span>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/dashboard\">\n                <Button variant=\"outline\">返回转换</Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主要内容 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">⚙️ 规则管理</h1>\n          <p className=\"mt-2 text-gray-600\">\n            管理转换规则，创建自定义规则或从模板导入\n          </p>\n        </div>\n\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-red-600 text-sm\">{error}</p>\n            <Button \n              variant=\"outline\" \n              size=\"sm\" \n              className=\"mt-2\"\n              onClick={() => setError(null)}\n            >\n              关闭\n            </Button>\n          </div>\n        )}\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"list\">规则列表</TabsTrigger>\n            <TabsTrigger value=\"templates\">规则模板</TabsTrigger>\n            <TabsTrigger value=\"editor\" disabled={!selectedRule}>\n              {isEditing ? \"编辑规则\" : \"规则详情\"}\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"list\" className=\"space-y-6\">\n            <div className=\"flex justify-between items-center\">\n              <h2 className=\"text-xl font-semibold\">我的规则</h2>\n              <Button onClick={handleCreateRule}>\n                ➕ 创建新规则\n              </Button>\n            </div>\n            \n            <RuleList\n              rules={rules}\n              onEdit={handleEditRule}\n              onDelete={handleDeleteRule}\n              onToggleEnabled={async (ruleId, enabled) => {\n                const rule = rules.find(r => r.id === ruleId);\n                if (rule) {\n                  await handleSaveRule({ ...rule, enabled });\n                }\n              }}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"templates\" className=\"space-y-6\">\n            <div>\n              <h2 className=\"text-xl font-semibold mb-4\">规则模板</h2>\n              <p className=\"text-gray-600 mb-6\">\n                从预设模板快速创建规则，或查看模板示例\n              </p>\n            </div>\n            \n            <RuleTemplates\n              templates={templates}\n              onCreateFromTemplate={handleCreateFromTemplate}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"editor\" className=\"space-y-6\">\n            {selectedRule && (\n              <RuleEditor\n                rule={selectedRule}\n                isEditing={isEditing}\n                onSave={handleSaveRule}\n                onCancel={() => {\n                  setIsEditing(false);\n                  setActiveTab(\"list\");\n                }}\n              />\n            )}\n          </TabsContent>\n        </Tabs>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAmCe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sCAAsC;gBACjE,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE;YAC9C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gDAAgD;gBAC3E,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE;YAC9C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,WAAW;YACX,MAAM,QAAQ,GAAG,CAAC;gBAAC;gBAAa;aAAgB;YAChD,WAAW;QACb;QAEA;IACF,GAAG,EAAE;IAEL,QAAQ;IACR,MAAM,mBAAmB;QACvB,MAAM,UAAgB;YACpB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,UAAU;YACV,YAAY,EAAE;YACd,SAAS,EAAE;YACX,MAAM,EAAE;QACV;QAEA,gBAAgB;QAChB,aAAa;QACb,aAAa;IACf;IAEA,OAAO;IACP,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,aAAa;QACb,aAAa;IACf;IAEA,UAAU;IACV,MAAM,2BAA2B,OAAO;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mDAAmD,EAAE,YAAY,EAAE;gBAC/F,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE;YAC9C;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,SAAS,CAAA,OAAQ;uBAAI;oBAAM;iBAAQ;YACnC,gBAAgB;YAChB,aAAa;YACb,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,YAAY,CAAC,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;YACnD,MAAM,MAAM,YACR,uCACA,CAAC,mCAAmC,EAAE,KAAK,EAAE,EAAE;YAEnD,MAAM,SAAS,YAAY,SAAS;YAEpC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM;gBACN,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAK;YAC9B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE;YAC9C;YAEA,MAAM,YAAY,MAAM,SAAS,IAAI;YAErC,IAAI,WAAW;gBACb,SAAS,CAAA,OAAQ;2BAAI;wBAAM;qBAAU;YACvC,OAAO;gBACL,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG,YAAY;YAChE;YAEA,aAAa;YACb,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,gBAAgB;YAC3B;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mCAAmC,EAAE,QAAQ,EAAE;gBAC3E,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE;YAC9C;YAEA,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAE3C,IAAI,cAAc,OAAO,QAAQ;gBAC/B,gBAAgB;gBAChB,aAAa;gBACb,aAAa;YACf;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAkC;;;;;;kDAG3D,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAKnC,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,SAAS;0CACzB;;;;;;;;;;;;kCAML,8OAAC,gIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAO;;;;;;kDAC1B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAY;;;;;;kDAC/B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAS,UAAU,CAAC;kDACpC,YAAY,SAAS;;;;;;;;;;;;0CAI1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;;kDAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DACtC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;0DAAkB;;;;;;;;;;;;kDAKrC,8OAAC,uIAAA,CAAA,UAAQ;wCACP,OAAO;wCACP,QAAQ;wCACR,UAAU;wCACV,iBAAiB,OAAO,QAAQ;4CAC9B,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;4CACtC,IAAI,MAAM;gDACR,MAAM,eAAe;oDAAE,GAAG,IAAI;oDAAE;gDAAQ;4CAC1C;wCACF;;;;;;;;;;;;0CAIJ,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAKpC,8OAAC,4IAAA,CAAA,UAAa;wCACZ,WAAW;wCACX,sBAAsB;;;;;;;;;;;;0CAI1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;0CACnC,8BACC,8OAAC,yIAAA,CAAA,UAAU;oCACT,MAAM;oCACN,WAAW;oCACX,QAAQ;oCACR,UAAU;wCACR,aAAa;wCACb,aAAa;oCACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}]}