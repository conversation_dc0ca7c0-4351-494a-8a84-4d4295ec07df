{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { ChevronDownIcon, ChevronUpIcon, CheckIcon } from \"@radix-ui/react-icons\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDownIcon className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUpIcon />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDownIcon />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <CheckIcon className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,gLAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIjC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gLAAA,CAAA,gBAAa;;;;;;;;;;AAGlB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gLAAA,CAAA,kBAAe;;;;;;;;;;AAGpB,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,gLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/rules/RuleTestModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\n\ninterface RuleExample {\n  input_text: string;\n  output_text: string;\n  description: string;\n}\n\ninterface SubRule {\n  id: string;\n  name: string;\n  description: string;\n  enabled: boolean;\n  implementation_type: string;\n  examples: RuleExample[];\n  test_cases: string[];\n}\n\ninterface TestResult {\n  original_text: string;\n  transformed_text: string;\n  rule_applied: boolean;\n  execution_time_ms: number;\n}\n\ninterface RuleTestModalProps {\n  subRule: SubRule;\n  onClose: () => void;\n}\n\nexport default function RuleTestModal({ subRule, onClose }: RuleTestModalProps) {\n  const [testText, setTestText] = useState(\"\");\n  const [testResult, setTestResult] = useState<TestResult | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedTestCase, setSelectedTestCase] = useState<string>(\"\");\n\n  // 获取测试用例\n  useEffect(() => {\n    if (subRule.test_cases.length > 0) {\n      setTestText(subRule.test_cases[0]);\n      setSelectedTestCase(subRule.test_cases[0]);\n    } else if (subRule.examples.length > 0) {\n      setTestText(subRule.examples[0].input_text);\n    }\n  }, [subRule]);\n\n  // 执行测试\n  const runTest = async () => {\n    if (!testText.trim()) {\n      setError(\"请输入测试文本\");\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setTestResult(null);\n\n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/simple-rules/sub-rule/test\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        mode: 'cors',\n        body: JSON.stringify({\n          sub_rule_id: subRule.id,\n          test_text: testText\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`测试失败: ${response.status}`);\n      }\n\n      const result = await response.json();\n      setTestResult(result);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"测试过程中发生错误\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 使用预设测试用例\n  const useTestCase = (testCase: string) => {\n    setTestText(testCase);\n    setSelectedTestCase(testCase);\n    setTestResult(null);\n  };\n\n  // 使用示例\n  const useExample = (example: RuleExample) => {\n    setTestText(example.input_text);\n    setSelectedTestCase(\"\");\n    setTestResult(null);\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n        <Card className=\"border-0 shadow-none\">\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <span>🧪 测试规则</span>\n                  <Badge \n                    variant={subRule.implementation_type === 'llm' ? 'default' : 'secondary'}\n                  >\n                    {subRule.implementation_type === 'llm' ? 'LLM' : '确定性'}\n                  </Badge>\n                </CardTitle>\n                <CardDescription>\n                  {subRule.name} - {subRule.description}\n                </CardDescription>\n              </div>\n              <Button variant=\"outline\" onClick={onClose}>\n                ✕ 关闭\n              </Button>\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"space-y-6\">\n            {/* 错误提示 */}\n            {error && (\n              <div className=\"p-4 bg-red-50 border border-red-200 rounded-md\">\n                <p className=\"text-red-600 text-sm\">{error}</p>\n              </div>\n            )}\n\n            {/* 测试用例选择 */}\n            {(subRule.test_cases.length > 0 || subRule.examples.length > 0) && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-base\">快速选择测试内容</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    {/* 预设测试用例 */}\n                    {subRule.test_cases.length > 0 && (\n                      <div>\n                        <Label className=\"text-sm font-medium mb-2 block\">预设测试用例</Label>\n                        <div className=\"space-y-2\">\n                          {subRule.test_cases.map((testCase, index) => (\n                            <Button\n                              key={index}\n                              variant={selectedTestCase === testCase ? \"default\" : \"outline\"}\n                              size=\"sm\"\n                              onClick={() => useTestCase(testCase)}\n                              className=\"w-full text-left justify-start h-auto p-2\"\n                            >\n                              <div className=\"text-xs truncate\">\n                                {testCase.length > 50 ? testCase.substring(0, 50) + \"...\" : testCase}\n                              </div>\n                            </Button>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* 示例 */}\n                    {subRule.examples.length > 0 && (\n                      <div>\n                        <Label className=\"text-sm font-medium mb-2 block\">转换示例</Label>\n                        <div className=\"space-y-2\">\n                          {subRule.examples.map((example, index) => (\n                            <Button\n                              key={index}\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => useExample(example)}\n                              className=\"w-full text-left justify-start h-auto p-2\"\n                            >\n                              <div className=\"text-xs\">\n                                <div className=\"font-medium\">{example.description}</div>\n                                <div className=\"text-gray-600 truncate\">\n                                  {example.input_text.length > 30 \n                                    ? example.input_text.substring(0, 30) + \"...\" \n                                    : example.input_text}\n                                </div>\n                              </div>\n                            </Button>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* 测试输入 */}\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <CardTitle className=\"text-base\">测试文本</CardTitle>\n                  <Button onClick={runTest} disabled={loading}>\n                    {loading ? \"测试中...\" : \"🧪 执行测试\"}\n                  </Button>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <Textarea\n                  value={testText}\n                  onChange={(e) => setTestText(e.target.value)}\n                  placeholder=\"输入要测试的文本...\"\n                  className=\"min-h-[120px] resize-none\"\n                />\n                <div className=\"text-sm text-gray-500 mt-2\">\n                  字符数: {testText.length}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* 测试结果 */}\n            {testResult && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-base\">测试结果</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {/* 执行统计 */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-blue-600\">\n                        {testResult.rule_applied ? \"✅\" : \"❌\"}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">规则应用</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-green-600\">\n                        {testResult.execution_time_ms.toFixed(2)}ms\n                      </div>\n                      <div className=\"text-sm text-gray-600\">执行时间</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-purple-600\">\n                        {testResult.transformed_text.length}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">结果字符数</div>\n                    </div>\n                  </div>\n\n                  {/* 对比结果 */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n                    <div>\n                      <Label className=\"text-sm font-medium text-gray-600 mb-2 block\">\n                        原始文本\n                      </Label>\n                      <div className=\"p-3 bg-gray-50 border rounded-md min-h-[120px] whitespace-pre-wrap text-sm\">\n                        {testResult.original_text}\n                      </div>\n                    </div>\n                    <div>\n                      <Label className=\"text-sm font-medium text-gray-600 mb-2 block\">\n                        转换结果\n                      </Label>\n                      <div className=\"p-3 bg-blue-50 border rounded-md min-h-[120px] whitespace-pre-wrap text-sm\">\n                        {testResult.transformed_text}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 变化说明 */}\n                  {testResult.rule_applied ? (\n                    <div className=\"mt-4 p-3 bg-green-50 border border-green-200 rounded-md\">\n                      <div className=\"text-green-800 text-sm\">\n                        ✅ 规则已成功应用，文本发生了变化\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\">\n                      <div className=\"text-yellow-800 text-sm\">\n                        ⚠️ 规则未应用，可能是条件不匹配或规则已禁用\n                      </div>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            )}\n\n            {/* 规则详情 */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-base\">规则详情</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <span className=\"font-medium text-gray-700\">规则名称:</span>\n                      <span className=\"ml-2\">{subRule.name}</span>\n                    </div>\n                    <div>\n                      <span className=\"font-medium text-gray-700\">实现方式:</span>\n                      <span className=\"ml-2\">\n                        {subRule.implementation_type === 'llm' ? 'LLM处理' : '确定性规则'}\n                      </span>\n                    </div>\n                    <div>\n                      <span className=\"font-medium text-gray-700\">启用状态:</span>\n                      <span className=\"ml-2\">{subRule.enabled ? '已启用' : '已禁用'}</span>\n                    </div>\n                    <div>\n                      <span className=\"font-medium text-gray-700\">示例数量:</span>\n                      <span className=\"ml-2\">{subRule.examples.length} 个</span>\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <span className=\"font-medium text-gray-700\">规则描述:</span>\n                    <p className=\"text-sm text-gray-600 mt-1\">{subRule.description}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* 操作按钮 */}\n            <div className=\"flex justify-end space-x-2\">\n              <Button variant=\"outline\" onClick={onClose}>\n                关闭\n              </Button>\n              <Button onClick={runTest} disabled={loading}>\n                {loading ? \"测试中...\" : \"重新测试\"}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAsCe,SAAS,cAAc,EAAE,OAAO,EAAE,OAAO,EAAsB;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;YACjC,YAAY,QAAQ,UAAU,CAAC,EAAE;YACjC,oBAAoB,QAAQ,UAAU,CAAC,EAAE;QAC3C,OAAO,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACtC,YAAY,QAAQ,QAAQ,CAAC,EAAE,CAAC,UAAU;QAC5C;IACF,GAAG;QAAC;KAAQ;IAEZ,OAAO;IACP,MAAM,UAAU;QACd,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2DAA2D;gBACtF,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM;gBACN,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa,QAAQ,EAAE;oBACvB,WAAW;gBACb;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,SAAS,MAAM,EAAE;YAC5C;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,WAAW;IACX,MAAM,cAAc,CAAC;QACnB,YAAY;QACZ,oBAAoB;QACpB,cAAc;IAChB;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,YAAY,QAAQ,UAAU;QAC9B,oBAAoB;QACpB,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC;8DAAK;;;;;;8DACN,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAS,QAAQ,mBAAmB,KAAK,QAAQ,YAAY;8DAE5D,QAAQ,mBAAmB,KAAK,QAAQ,QAAQ;;;;;;;;;;;;sDAGrD,8OAAC,gIAAA,CAAA,kBAAe;;gDACb,QAAQ,IAAI;gDAAC;gDAAI,QAAQ,WAAW;;;;;;;;;;;;;8CAGzC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAS;;;;;;;;;;;;;;;;;kCAMhD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BAEpB,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;4BAKxC,CAAC,QAAQ,UAAU,CAAC,MAAM,GAAG,KAAK,QAAQ,QAAQ,CAAC,MAAM,GAAG,CAAC,mBAC5D,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY;;;;;;;;;;;kDAEnC,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;gDAEZ,QAAQ,UAAU,CAAC,MAAM,GAAG,mBAC3B,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAiC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjC,8OAAC,kIAAA,CAAA,SAAM;oEAEL,SAAS,qBAAqB,WAAW,YAAY;oEACrD,MAAK;oEACL,SAAS,IAAM,YAAY;oEAC3B,WAAU;8EAEV,cAAA,8OAAC;wEAAI,WAAU;kFACZ,SAAS,MAAM,GAAG,KAAK,SAAS,SAAS,CAAC,GAAG,MAAM,QAAQ;;;;;;mEAPzD;;;;;;;;;;;;;;;;gDAgBd,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAiC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,kIAAA,CAAA,SAAM;oEAEL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,WAAW;oEAC1B,WAAU;8EAEV,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAe,QAAQ,WAAW;;;;;;0FACjD,8OAAC;gFAAI,WAAU;0FACZ,QAAQ,UAAU,CAAC,MAAM,GAAG,KACzB,QAAQ,UAAU,CAAC,SAAS,CAAC,GAAG,MAAM,QACtC,QAAQ,UAAU;;;;;;;;;;;;mEAXrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAyBvB,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAY;;;;;;8DACjC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS;oDAAS,UAAU;8DACjC,UAAU,WAAW;;;;;;;;;;;;;;;;;kDAI5B,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC,oIAAA,CAAA,WAAQ;gDACP,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;oDAA6B;oDACpC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;4BAM1B,4BACC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY;;;;;;;;;;;kDAEnC,8OAAC,gIAAA,CAAA,cAAW;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,WAAW,YAAY,GAAG,MAAM;;;;;;0EAEnC,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEACZ,WAAW,iBAAiB,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EAE3C,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,WAAW,gBAAgB,CAAC,MAAM;;;;;;0EAErC,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAK3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEAAI,WAAU;0EACZ,WAAW,aAAa;;;;;;;;;;;;kEAG7B,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEAAI,WAAU;0EACZ,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;4CAMjC,WAAW,YAAY,iBACtB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;qEAK1C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;0CAUnD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY;;;;;;;;;;;kDAEnC,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;8EAC5C,8OAAC;oEAAK,WAAU;8EAAQ,QAAQ,IAAI;;;;;;;;;;;;sEAEtC,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;8EAC5C,8OAAC;oEAAK,WAAU;8EACb,QAAQ,mBAAmB,KAAK,QAAQ,UAAU;;;;;;;;;;;;sEAGvD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;8EAC5C,8OAAC;oEAAK,WAAU;8EAAQ,QAAQ,OAAO,GAAG,QAAQ;;;;;;;;;;;;sEAEpD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;8EAC5C,8OAAC;oEAAK,WAAU;;wEAAQ,QAAQ,QAAQ,CAAC,MAAM;wEAAC;;;;;;;;;;;;;;;;;;;8DAIpD,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAE,WAAU;sEAA8B,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;kDAAS;;;;;;kDAG5C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAS,UAAU;kDACjC,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/rules/SimplifiedRuleManager.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport RuleTestModal from \"./RuleTestModal\";\n\ninterface RuleExample {\n  input_text: string;\n  output_text: string;\n  description: string;\n}\n\ninterface SubRule {\n  id: string;\n  name: string;\n  description: string;\n  enabled: boolean;\n  implementation_type: string;\n  examples: RuleExample[];\n  test_cases: string[];\n}\n\ninterface MainRule {\n  id: string;\n  name: string;\n  description: string;\n  enabled: boolean;\n  sub_rules: SubRule[];\n}\n\ninterface RuleCategory {\n  id: string;\n  name: string;\n  description: string;\n  icon: string;\n  enabled: boolean;\n  main_rules: MainRule[];\n}\n\ninterface RuleSet {\n  id: string;\n  name: string;\n  description: string;\n  is_default: boolean;\n  categories: RuleCategory[];\n}\n\ninterface SavedRuleSet {\n  id: string;\n  name: string;\n  description: string;\n  is_default: boolean;\n}\n\nexport default function SimplifiedRuleManager() {\n  const [currentRuleSet, setCurrentRuleSet] = useState<RuleSet | null>(null);\n  const [savedRuleSets, setSavedRuleSets] = useState<SavedRuleSet[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());\n  const [expandedMainRules, setExpandedMainRules] = useState<Set<string>>(new Set());\n  const [showSaveDialog, setShowSaveDialog] = useState(false);\n  const [newRuleSetName, setNewRuleSetName] = useState(\"\");\n  const [newRuleSetDescription, setNewRuleSetDescription] = useState(\"\");\n  const [testingSubRule, setTestingSubRule] = useState<SubRule | null>(null);\n  const [statistics, setStatistics] = useState<any>(null);\n\n  // 加载当前规则集\n  const loadCurrentRuleSet = async () => {\n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/simple-rules/current\", {\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`加载规则集失败: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      setCurrentRuleSet(data);\n      \n      // 默认展开第一个分类\n      if (data.categories.length > 0) {\n        setExpandedCategories(new Set([data.categories[0].id]));\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"加载规则集失败\");\n    }\n  };\n\n  // 加载已保存的规则集\n  const loadSavedRuleSets = async () => {\n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/simple-rules/saved\", {\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`加载已保存规则集失败: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      setSavedRuleSets(data);\n    } catch (err) {\n      console.error(\"加载已保存规则集失败:\", err);\n    }\n  };\n\n  // 加载统计信息\n  const loadStatistics = async () => {\n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/simple-rules/statistics\", {\n        mode: 'cors',\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data);\n      }\n    } catch (err) {\n      console.error(\"加载统计信息失败:\", err);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      await Promise.all([\n        loadCurrentRuleSet(),\n        loadSavedRuleSets(),\n        loadStatistics()\n      ]);\n      setLoading(false);\n    };\n    \n    loadData();\n  }, []);\n\n  // 切换规则集\n  const switchRuleSet = async (ruleSetId: string) => {\n    try {\n      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/switch/${ruleSetId}`, {\n        method: 'POST',\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`切换规则集失败: ${response.status}`);\n      }\n      \n      await loadCurrentRuleSet();\n      await loadStatistics();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"切换规则集失败\");\n    }\n  };\n\n  // 保存当前规则集\n  const saveCurrentRuleSet = async () => {\n    if (!newRuleSetName.trim()) {\n      setError(\"请输入规则集名称\");\n      return;\n    }\n    \n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/simple-rules/save\", {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        mode: 'cors',\n        body: JSON.stringify({\n          name: newRuleSetName,\n          description: newRuleSetDescription\n        }),\n      });\n      \n      if (!response.ok) {\n        throw new Error(`保存规则集失败: ${response.status}`);\n      }\n      \n      await loadSavedRuleSets();\n      setShowSaveDialog(false);\n      setNewRuleSetName(\"\");\n      setNewRuleSetDescription(\"\");\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"保存规则集失败\");\n    }\n  };\n\n  // 切换分类启用状态\n  const toggleCategoryEnabled = async (categoryId: string, enabled: boolean) => {\n    try {\n      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/category/${categoryId}/enabled?enabled=${enabled}`, {\n        method: 'PUT',\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`切换分类状态失败: ${response.status}`);\n      }\n      \n      await loadCurrentRuleSet();\n      await loadStatistics();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"切换分类状态失败\");\n    }\n  };\n\n  // 切换主规则启用状态\n  const toggleMainRuleEnabled = async (mainRuleId: string, enabled: boolean) => {\n    try {\n      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/main-rule/${mainRuleId}/enabled?enabled=${enabled}`, {\n        method: 'PUT',\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`切换主规则状态失败: ${response.status}`);\n      }\n      \n      await loadCurrentRuleSet();\n      await loadStatistics();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"切换主规则状态失败\");\n    }\n  };\n\n  // 切换子规则启用状态\n  const toggleSubRuleEnabled = async (subRuleId: string, enabled: boolean) => {\n    try {\n      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/sub-rule/${subRuleId}/enabled?enabled=${enabled}`, {\n        method: 'PUT',\n        mode: 'cors',\n      });\n      \n      if (!response.ok) {\n        throw new Error(`切换子规则状态失败: ${response.status}`);\n      }\n      \n      await loadCurrentRuleSet();\n      await loadStatistics();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"切换子规则状态失败\");\n    }\n  };\n\n  // 切换分类展开状态\n  const toggleCategoryExpanded = (categoryId: string) => {\n    const newExpanded = new Set(expandedCategories);\n    if (newExpanded.has(categoryId)) {\n      newExpanded.delete(categoryId);\n    } else {\n      newExpanded.add(categoryId);\n    }\n    setExpandedCategories(newExpanded);\n  };\n\n  // 切换主规则展开状态\n  const toggleMainRuleExpanded = (mainRuleId: string) => {\n    const newExpanded = new Set(expandedMainRules);\n    if (newExpanded.has(mainRuleId)) {\n      newExpanded.delete(mainRuleId);\n    } else {\n      newExpanded.add(mainRuleId);\n    }\n    setExpandedMainRules(newExpanded);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <div className=\"text-center\">\n          <div className=\"text-4xl mb-4\">⚙️</div>\n          <p className=\"text-gray-600\">加载规则管理器...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!currentRuleSet) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-4xl mb-4\">❌</div>\n        <p className=\"text-gray-600\">无法加载规则集</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 错误提示 */}\n      {error && (\n        <div className=\"p-4 bg-red-50 border border-red-200 rounded-md\">\n          <p className=\"text-red-600 text-sm\">{error}</p>\n          <Button \n            variant=\"outline\" \n            size=\"sm\" \n            className=\"mt-2\"\n            onClick={() => setError(null)}\n          >\n            关闭\n          </Button>\n        </div>\n      )}\n\n      {/* 顶部控制区域 */}\n      <Card>\n        <CardHeader>\n          <CardTitle>规则集管理</CardTitle>\n          <CardDescription>\n            当前规则集：{currentRuleSet.name}\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {/* 规则集选择 */}\n            <div>\n              <Label className=\"text-sm font-medium mb-2 block\">切换规则集</Label>\n              <Select value={currentRuleSet.id} onValueChange={switchRuleSet}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  {savedRuleSets.map((ruleSet) => (\n                    <SelectItem key={ruleSet.id} value={ruleSet.id}>\n                      {ruleSet.name} {ruleSet.is_default && \"(默认)\"}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* 保存按钮 */}\n            <div className=\"flex items-end\">\n              <Button \n                onClick={() => setShowSaveDialog(true)}\n                className=\"w-full\"\n              >\n                💾 保存为新规则集\n              </Button>\n            </div>\n\n            {/* 统计信息 */}\n            <div className=\"text-sm\">\n              {statistics && (\n                <div className=\"space-y-1\">\n                  <div>规则大类: {statistics.categories.enabled}/{statistics.categories.total}</div>\n                  <div>一级规则: {statistics.main_rules.enabled}/{statistics.main_rules.total}</div>\n                  <div>二级规则: {statistics.sub_rules.enabled}/{statistics.sub_rules.total}</div>\n                </div>\n              )}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 保存对话框 */}\n      {showSaveDialog && (\n        <Card className=\"border-blue-200 bg-blue-50\">\n          <CardHeader>\n            <CardTitle>保存规则集</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"ruleset-name\">规则集名称</Label>\n              <Input\n                id=\"ruleset-name\"\n                value={newRuleSetName}\n                onChange={(e) => setNewRuleSetName(e.target.value)}\n                placeholder=\"输入规则集名称\"\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"ruleset-description\">规则集描述</Label>\n              <Input\n                id=\"ruleset-description\"\n                value={newRuleSetDescription}\n                onChange={(e) => setNewRuleSetDescription(e.target.value)}\n                placeholder=\"输入规则集描述（可选）\"\n              />\n            </div>\n            <div className=\"flex space-x-2\">\n              <Button onClick={saveCurrentRuleSet}>保存</Button>\n              <Button variant=\"outline\" onClick={() => setShowSaveDialog(false)}>取消</Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* 规则层级展示 */}\n      <div className=\"space-y-4\">\n        {currentRuleSet.categories.map((category) => (\n          <CategoryCard\n            key={category.id}\n            category={category}\n            isExpanded={expandedCategories.has(category.id)}\n            onToggleExpanded={() => toggleCategoryExpanded(category.id)}\n            onToggleEnabled={(enabled) => toggleCategoryEnabled(category.id, enabled)}\n            expandedMainRules={expandedMainRules}\n            onToggleMainRuleExpanded={toggleMainRuleExpanded}\n            onToggleMainRuleEnabled={toggleMainRuleEnabled}\n            onToggleSubRuleEnabled={toggleSubRuleEnabled}\n            onTestSubRule={setTestingSubRule}\n          />\n        ))}\n      </div>\n\n      {/* 测试模态框 */}\n      {testingSubRule && (\n        <RuleTestModal\n          subRule={testingSubRule}\n          onClose={() => setTestingSubRule(null)}\n        />\n      )}\n    </div>\n  );\n}\n\n// 分类卡片组件\nfunction CategoryCard({ \n  category, \n  isExpanded, \n  onToggleExpanded, \n  onToggleEnabled,\n  expandedMainRules,\n  onToggleMainRuleExpanded,\n  onToggleMainRuleEnabled,\n  onToggleSubRuleEnabled,\n  onTestSubRule\n}: {\n  category: RuleCategory;\n  isExpanded: boolean;\n  onToggleExpanded: () => void;\n  onToggleEnabled: (enabled: boolean) => void;\n  expandedMainRules: Set<string>;\n  onToggleMainRuleExpanded: (mainRuleId: string) => void;\n  onToggleMainRuleEnabled: (mainRuleId: string, enabled: boolean) => void;\n  onToggleSubRuleEnabled: (subRuleId: string, enabled: boolean) => void;\n  onTestSubRule: (subRule: SubRule) => void;\n}) {\n  const enabledMainRules = category.main_rules.filter(rule => rule.enabled).length;\n  const totalMainRules = category.main_rules.length;\n\n  return (\n    <Card className={`transition-all ${!category.enabled ? 'opacity-60' : ''}`}>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onToggleExpanded}\n              className=\"p-1\"\n            >\n              {isExpanded ? \"📂\" : \"📁\"}\n            </Button>\n            <div>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <span>{category.icon}</span>\n                <span>{category.name}</span>\n                <Badge variant=\"outline\">\n                  {enabledMainRules}/{totalMainRules}\n                </Badge>\n              </CardTitle>\n              <CardDescription>{category.description}</CardDescription>\n            </div>\n          </div>\n          <Switch\n            checked={category.enabled}\n            onCheckedChange={onToggleEnabled}\n          />\n        </div>\n      </CardHeader>\n      \n      {isExpanded && (\n        <CardContent>\n          <div className=\"space-y-3\">\n            {category.main_rules.map((mainRule) => (\n              <MainRuleCard\n                key={mainRule.id}\n                mainRule={mainRule}\n                isExpanded={expandedMainRules.has(mainRule.id)}\n                onToggleExpanded={() => onToggleMainRuleExpanded(mainRule.id)}\n                onToggleEnabled={(enabled) => onToggleMainRuleEnabled(mainRule.id, enabled)}\n                onToggleSubRuleEnabled={onToggleSubRuleEnabled}\n                onTestSubRule={onTestSubRule}\n              />\n            ))}\n          </div>\n        </CardContent>\n      )}\n    </Card>\n  );\n}\n\n// 主规则卡片组件\nfunction MainRuleCard({\n  mainRule,\n  isExpanded,\n  onToggleExpanded,\n  onToggleEnabled,\n  onToggleSubRuleEnabled,\n  onTestSubRule\n}: {\n  mainRule: MainRule;\n  isExpanded: boolean;\n  onToggleExpanded: () => void;\n  onToggleEnabled: (enabled: boolean) => void;\n  onToggleSubRuleEnabled: (subRuleId: string, enabled: boolean) => void;\n  onTestSubRule: (subRule: SubRule) => void;\n}) {\n  const enabledSubRules = mainRule.sub_rules.filter(rule => rule.enabled).length;\n  const totalSubRules = mainRule.sub_rules.length;\n\n  return (\n    <div className={`border rounded-lg p-4 ${!mainRule.enabled ? 'opacity-60' : ''}`}>\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex items-center space-x-3\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onToggleExpanded}\n            className=\"p-1\"\n          >\n            {isExpanded ? \"📖\" : \"📕\"}\n          </Button>\n          <div>\n            <div className=\"font-medium flex items-center space-x-2\">\n              <span>{mainRule.name}</span>\n              <Badge variant=\"outline\" className=\"text-xs\">\n                {enabledSubRules}/{totalSubRules}\n              </Badge>\n            </div>\n            <div className=\"text-sm text-gray-600\">{mainRule.description}</div>\n          </div>\n        </div>\n        <Switch\n          checked={mainRule.enabled}\n          onCheckedChange={onToggleEnabled}\n        />\n      </div>\n      \n      {isExpanded && (\n        <div className=\"ml-8 space-y-2\">\n          {mainRule.sub_rules.map((subRule) => (\n            <SubRuleCard\n              key={subRule.id}\n              subRule={subRule}\n              onToggleEnabled={(enabled) => onToggleSubRuleEnabled(subRule.id, enabled)}\n              onTest={() => onTestSubRule(subRule)}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\n// 子规则卡片组件\nfunction SubRuleCard({\n  subRule,\n  onToggleEnabled,\n  onTest\n}: {\n  subRule: SubRule;\n  onToggleEnabled: (enabled: boolean) => void;\n  onTest: () => void;\n}) {\n  const [showExamples, setShowExamples] = useState(false);\n\n  return (\n    <div className={`border rounded p-3 ${!subRule.enabled ? 'opacity-60' : ''}`}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <div className=\"flex items-center space-x-2 mb-1\">\n            <span className=\"font-medium text-sm\">{subRule.name}</span>\n            <Badge \n              variant={subRule.implementation_type === 'llm' ? 'default' : 'secondary'}\n              className=\"text-xs\"\n            >\n              {subRule.implementation_type === 'llm' ? 'LLM' : '确定性'}\n            </Badge>\n          </div>\n          <div className=\"text-xs text-gray-600 mb-2\">{subRule.description}</div>\n          \n          <div className=\"flex space-x-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setShowExamples(!showExamples)}\n              className=\"text-xs\"\n            >\n              {showExamples ? \"隐藏\" : \"显示\"}示例\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={onTest}\n              className=\"text-xs\"\n            >\n              🧪 测试\n            </Button>\n          </div>\n        </div>\n        <Switch\n          checked={subRule.enabled}\n          onCheckedChange={onToggleEnabled}\n        />\n      </div>\n      \n      {showExamples && subRule.examples.length > 0 && (\n        <div className=\"mt-3 space-y-2\">\n          {subRule.examples.map((example, index) => (\n            <div key={index} className=\"bg-gray-50 p-2 rounded text-xs\">\n              <div className=\"font-medium text-gray-700 mb-1\">{example.description}</div>\n              <div className=\"space-y-1\">\n                <div><span className=\"text-gray-600\">输入:</span> {example.input_text}</div>\n                <div><span className=\"text-gray-600\">输出:</span> {example.output_text}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAXA;;;;;;;;;;;AA6De,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC9E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAElD,UAAU;IACV,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qDAAqD;gBAChF,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,kBAAkB;YAElB,YAAY;YACZ,IAAI,KAAK,UAAU,CAAC,MAAM,GAAG,GAAG;gBAC9B,sBAAsB,IAAI,IAAI;oBAAC,KAAK,UAAU,CAAC,EAAE,CAAC,EAAE;iBAAC;YACvD;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,YAAY;IACZ,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mDAAmD;gBAC9E,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,SAAS,MAAM,EAAE;YAClD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,iBAAiB;QACnB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,eAAe;QAC/B;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wDAAwD;gBACnF,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,cAAc;YAChB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,WAAW;YACX,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;gBACA;aACD;YACD,WAAW;QACb;QAEA;IACF,GAAG,EAAE;IAEL,QAAQ;IACR,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,iDAAiD,EAAE,WAAW,EAAE;gBAC5F,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE;YAC/C;YAEA,MAAM;YACN,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,UAAU;IACV,MAAM,qBAAqB;QACzB,IAAI,CAAC,eAAe,IAAI,IAAI;YAC1B,SAAS;YACT;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kDAAkD;gBAC7E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM;gBACN,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE;YAC/C;YAEA,MAAM;YACN,kBAAkB;YAClB,kBAAkB;YAClB,yBAAyB;QAC3B,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,WAAW;IACX,MAAM,wBAAwB,OAAO,YAAoB;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mDAAmD,EAAE,WAAW,iBAAiB,EAAE,SAAS,EAAE;gBAC1H,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,MAAM,EAAE;YAChD;YAEA,MAAM;YACN,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,YAAY;IACZ,MAAM,wBAAwB,OAAO,YAAoB;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oDAAoD,EAAE,WAAW,iBAAiB,EAAE,SAAS,EAAE;gBAC3H,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE;YACjD;YAEA,MAAM;YACN,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,YAAY;IACZ,MAAM,uBAAuB,OAAO,WAAmB;QACrD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mDAAmD,EAAE,UAAU,iBAAiB,EAAE,SAAS,EAAE;gBACzH,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE;YACjD;YAEA,MAAM;YACN,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,WAAW;IACX,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,aAAa;YAC/B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,sBAAsB;IACxB;IAEA,YAAY;IACZ,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,aAAa;YAC/B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,qBAAqB;IACvB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,SAAS;kCACzB;;;;;;;;;;;;0BAOL,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;;oCAAC;oCACR,eAAe,IAAI;;;;;;;;;;;;;kCAG9B,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAiC;;;;;;sDAClD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO,eAAe,EAAE;4CAAE,eAAe;;8DAC/C,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;8DACX,cAAc,GAAG,CAAC,CAAC,wBAClB,8OAAC,kIAAA,CAAA,aAAU;4DAAkB,OAAO,QAAQ,EAAE;;gEAC3C,QAAQ,IAAI;gEAAC;gEAAE,QAAQ,UAAU,IAAI;;2DADvB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;8CASnC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAU;kDACX;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;8CACZ,4BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAI;oDAAO,WAAW,UAAU,CAAC,OAAO;oDAAC;oDAAE,WAAW,UAAU,CAAC,KAAK;;;;;;;0DACvE,8OAAC;;oDAAI;oDAAO,WAAW,UAAU,CAAC,OAAO;oDAAC;oDAAE,WAAW,UAAU,CAAC,KAAK;;;;;;;0DACvE,8OAAC;;oDAAI;oDAAO,WAAW,SAAS,CAAC,OAAO;oDAAC;oDAAE,WAAW,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAShF,gCACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAe;;;;;;kDAC9B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,aAAY;;;;;;;;;;;;0CAGhB,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAsB;;;;;;kDACrC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;wCACxD,aAAY;;;;;;;;;;;;0CAGhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;kDAAoB;;;;;;kDACrC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,kBAAkB;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAO3E,8OAAC;gBAAI,WAAU;0BACZ,eAAe,UAAU,CAAC,GAAG,CAAC,CAAC,yBAC9B,8OAAC;wBAEC,UAAU;wBACV,YAAY,mBAAmB,GAAG,CAAC,SAAS,EAAE;wBAC9C,kBAAkB,IAAM,uBAAuB,SAAS,EAAE;wBAC1D,iBAAiB,CAAC,UAAY,sBAAsB,SAAS,EAAE,EAAE;wBACjE,mBAAmB;wBACnB,0BAA0B;wBAC1B,yBAAyB;wBACzB,wBAAwB;wBACxB,eAAe;uBATV,SAAS,EAAE;;;;;;;;;;YAerB,gCACC,8OAAC,4IAAA,CAAA,UAAa;gBACZ,SAAS;gBACT,SAAS,IAAM,kBAAkB;;;;;;;;;;;;AAK3C;AAEA,SAAS;AACT,SAAS,aAAa,EACpB,QAAQ,EACR,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,wBAAwB,EACxB,uBAAuB,EACvB,sBAAsB,EACtB,aAAa,EAWd;IACC,MAAM,mBAAmB,SAAS,UAAU,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,MAAM;IAChF,MAAM,iBAAiB,SAAS,UAAU,CAAC,MAAM;IAEjD,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,eAAe,EAAE,CAAC,SAAS,OAAO,GAAG,eAAe,IAAI;;0BACxE,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAET,aAAa,OAAO;;;;;;8CAEvB,8OAAC;;sDACC,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC;8DAAM,SAAS,IAAI;;;;;;8DACpB,8OAAC;8DAAM,SAAS,IAAI;;;;;;8DACpB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDACZ;wDAAiB;wDAAE;;;;;;;;;;;;;sDAGxB,8OAAC,gIAAA,CAAA,kBAAe;sDAAE,SAAS,WAAW;;;;;;;;;;;;;;;;;;sCAG1C,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,SAAS,OAAO;4BACzB,iBAAiB;;;;;;;;;;;;;;;;;YAKtB,4BACC,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,yBACxB,8OAAC;4BAEC,UAAU;4BACV,YAAY,kBAAkB,GAAG,CAAC,SAAS,EAAE;4BAC7C,kBAAkB,IAAM,yBAAyB,SAAS,EAAE;4BAC5D,iBAAiB,CAAC,UAAY,wBAAwB,SAAS,EAAE,EAAE;4BACnE,wBAAwB;4BACxB,eAAe;2BANV,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;AAchC;AAEA,UAAU;AACV,SAAS,aAAa,EACpB,QAAQ,EACR,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,sBAAsB,EACtB,aAAa,EAQd;IACC,MAAM,kBAAkB,SAAS,SAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,MAAM;IAC9E,MAAM,gBAAgB,SAAS,SAAS,CAAC,MAAM;IAE/C,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,CAAC,SAAS,OAAO,GAAG,eAAe,IAAI;;0BAC9E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAET,aAAa,OAAO;;;;;;0CAEvB,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAM,SAAS,IAAI;;;;;;0DACpB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAChC;oDAAgB;oDAAE;;;;;;;;;;;;;kDAGvB,8OAAC;wCAAI,WAAU;kDAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;;kCAGhE,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,SAAS,OAAO;wBACzB,iBAAiB;;;;;;;;;;;;YAIpB,4BACC,8OAAC;gBAAI,WAAU;0BACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,wBACvB,8OAAC;wBAEC,SAAS;wBACT,iBAAiB,CAAC,UAAY,uBAAuB,QAAQ,EAAE,EAAE;wBACjE,QAAQ,IAAM,cAAc;uBAHvB,QAAQ,EAAE;;;;;;;;;;;;;;;;AAU7B;AAEA,UAAU;AACV,SAAS,YAAY,EACnB,OAAO,EACP,eAAe,EACf,MAAM,EAKP;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE,8OAAC;QAAI,WAAW,CAAC,mBAAmB,EAAE,CAAC,QAAQ,OAAO,GAAG,eAAe,IAAI;;0BAC1E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAuB,QAAQ,IAAI;;;;;;kDACnD,8OAAC,iIAAA,CAAA,QAAK;wCACJ,SAAS,QAAQ,mBAAmB,KAAK,QAAQ,YAAY;wCAC7D,WAAU;kDAET,QAAQ,mBAAmB,KAAK,QAAQ,QAAQ;;;;;;;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;0CAA8B,QAAQ,WAAW;;;;;;0CAEhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;4CAET,eAAe,OAAO;4CAAK;;;;;;;kDAE9B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAKL,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,QAAQ,OAAO;wBACxB,iBAAiB;;;;;;;;;;;;YAIpB,gBAAgB,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzC,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCAAI,WAAU;0CAAkC,QAAQ,WAAW;;;;;;0CACpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DAAI,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAU;4CAAE,QAAQ,UAAU;;;;;;;kDACnE,8OAAC;;0DAAI,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAU;4CAAE,QAAQ,WAAW;;;;;;;;;;;;;;uBAJ9D;;;;;;;;;;;;;;;;AAYtB", "debugId": null}}, {"offset": {"line": 2401, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/app/rules/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport Link from \"next/link\";\nimport SimplifiedRuleManager from \"@/components/rules/SimplifiedRuleManager\";\n\nexport default function RulesPage() {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 初始化加载\n  useEffect(() => {\n    // 简化版本不需要预加载，直接设置为加载完成\n    setLoading(false);\n  }, []);\n\n\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-4xl mb-4\">⚙️</div>\n          <p className=\"text-gray-600\">加载规则管理器...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                📝 笔录转换系统\n              </Link>\n              <span className=\"ml-4 text-gray-500\">/</span>\n              <span className=\"ml-4 text-gray-700\">规则管理</span>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/dashboard\">\n                <Button variant=\"outline\">返回转换</Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主要内容 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">⚙️ 规则管理</h1>\n          <p className=\"mt-2 text-gray-600\">\n            层级化规则配置，简洁高效的转换规则管理\n          </p>\n        </div>\n\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-red-600 text-sm\">{error}</p>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"mt-2\"\n              onClick={() => setError(null)}\n            >\n              关闭\n            </Button>\n          </div>\n        )}\n\n        {/* 简化的规则管理器 */}\n        <SimplifiedRuleManager />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAZA;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uBAAuB;QACvB,WAAW;IACb,GAAG,EAAE;IAIL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAkC;;;;;;kDAG3D,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAKnC,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,SAAS;0CACzB;;;;;;;;;;;;kCAOL,8OAAC,oJAAA,CAAA,UAAqB;;;;;;;;;;;;;;;;;AAI9B", "debugId": null}}]}