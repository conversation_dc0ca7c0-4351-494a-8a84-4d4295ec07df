{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/FileUpload.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useCallback } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Label } from \"@/components/ui/label\";\nimport { useDropzone } from \"react-dropzone\";\n\ninterface FileUploadProps {\n  onFileUploaded: (fileInfo: any) => void;\n  onError: (error: string) => void;\n}\n\ninterface FileInfo {\n  file_id: string;\n  original_filename: string;\n  file_size: number;\n  file_type: string;\n  character_count: number;\n  line_count: number;\n  text_preview: string;\n  upload_time: string;\n}\n\nexport default function FileUpload({ onFileUploaded, onError }: FileUploadProps) {\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (acceptedFiles.length === 0) return;\n\n    const file = acceptedFiles[0];\n    \n    // 验证文件类型\n    const allowedTypes = ['text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!allowedTypes.includes(file.type)) {\n      onError(\"不支持的文件类型。请上传 .txt 或 .docx 文件\");\n      return;\n    }\n\n    // 验证文件大小 (10MB)\n    const maxSize = 10 * 1024 * 1024;\n    if (file.size > maxSize) {\n      onError(\"文件大小超过限制 (10MB)\");\n      return;\n    }\n\n    setIsUploading(true);\n    setUploadProgress(0);\n\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      // 模拟上传进度\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(progressInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      const response = await fetch('http://localhost:8002/api/v1/file/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      clearInterval(progressInterval);\n      setUploadProgress(100);\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '上传失败');\n      }\n\n      const fileInfo: FileInfo = await response.json();\n      onFileUploaded(fileInfo);\n\n    } catch (error) {\n      onError(error instanceof Error ? error.message : '上传过程中发生错误');\n    } finally {\n      setIsUploading(false);\n      setUploadProgress(0);\n    }\n  }, [onFileUploaded, onError]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'text/plain': ['.txt'],\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']\n    },\n    multiple: false,\n    disabled: isUploading\n  });\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>📁 文件上传</CardTitle>\n        <CardDescription>\n          支持 .txt 和 .docx 格式，最大 10MB\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div\n          {...getRootProps()}\n          className={`\n            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n            ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}\n            ${isUploading ? 'pointer-events-none opacity-50' : ''}\n          `}\n        >\n          <input {...getInputProps()} />\n          \n          {isUploading ? (\n            <div className=\"space-y-4\">\n              <div className=\"text-2xl\">⏳</div>\n              <div>\n                <p className=\"text-sm text-gray-600 mb-2\">上传中...</p>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div \n                    className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${uploadProgress}%` }}\n                  ></div>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-1\">{uploadProgress}%</p>\n              </div>\n            </div>\n          ) : isDragActive ? (\n            <div className=\"space-y-2\">\n              <div className=\"text-4xl\">📂</div>\n              <p className=\"text-lg font-medium text-blue-600\">释放文件以上传</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              <div className=\"text-4xl\">📄</div>\n              <div>\n                <p className=\"text-lg font-medium text-gray-700 mb-2\">\n                  拖拽文件到这里，或点击选择文件\n                </p>\n                <p className=\"text-sm text-gray-500\">\n                  支持 .txt 和 .docx 格式\n                </p>\n              </div>\n              <Button variant=\"outline\" disabled={isUploading}>\n                选择文件\n              </Button>\n            </div>\n          )}\n        </div>\n\n        <div className=\"mt-4 text-xs text-gray-500\">\n          <p>• 支持的格式：.txt (文本文件)、.docx (Word文档)</p>\n          <p>• 文件大小限制：10MB</p>\n          <p>• 上传后将自动提取文本内容用于转换</p>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;;;AANA;;;;;AAwBe,SAAS,WAAW,EAAE,cAAc,EAAE,OAAO,EAAmB;;IAC7E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,OAAO;YAChC,IAAI,cAAc,MAAM,KAAK,GAAG;YAEhC,MAAM,OAAO,aAAa,CAAC,EAAE;YAE7B,SAAS;YACT,MAAM,eAAe;gBAAC;gBAAc;aAA0E;YAC9G,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,QAAQ;gBACR;YACF;YAEA,gBAAgB;YAChB,MAAM,UAAU,KAAK,OAAO;YAC5B,IAAI,KAAK,IAAI,GAAG,SAAS;gBACvB,QAAQ;gBACR;YACF;YAEA,eAAe;YACf,kBAAkB;YAElB,IAAI;gBACF,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,SAAS;gBACT,MAAM,mBAAmB;uEAAY;wBACnC;+EAAkB,CAAA;gCAChB,IAAI,QAAQ,IAAI;oCACd,cAAc;oCACd,OAAO;gCACT;gCACA,OAAO,OAAO;4BAChB;;oBACF;sEAAG;gBAEH,MAAM,WAAW,MAAM,MAAM,4CAA4C;oBACvE,QAAQ;oBACR,MAAM;gBACR;gBAEA,cAAc;gBACd,kBAAkB;gBAElB,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;gBACtC;gBAEA,MAAM,WAAqB,MAAM,SAAS,IAAI;gBAC9C,eAAe;YAEjB,EAAE,OAAO,OAAO;gBACd,QAAQ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACnD,SAAU;gBACR,eAAe;gBACf,kBAAkB;YACpB;QACF;yCAAG;QAAC;QAAgB;KAAQ;IAE5B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,cAAc;gBAAC;aAAO;YACtB,2EAA2E;gBAAC;aAAQ;QACtF;QACA,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;;kCACV,6LAAC;wBACE,GAAG,cAAc;wBAClB,WAAW,CAAC;;YAEV,EAAE,eAAe,+BAA+B,wCAAwC;YACxF,EAAE,cAAc,mCAAmC,GAAG;UACxD,CAAC;;0CAED,6LAAC;gCAAO,GAAG,eAAe;;;;;;4BAEzB,4BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAW;;;;;;kDAC1B,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGzC,6LAAC;gDAAE,WAAU;;oDAA8B;oDAAe;;;;;;;;;;;;;;;;;;uCAG5D,6BACF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAW;;;;;;kDAC1B,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;qDAGnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAW;;;;;;kDAC1B,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyC;;;;;;0DAGtD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,UAAU;kDAAa;;;;;;;;;;;;;;;;;;kCAOvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKb;GA3IwB;;QAiEgC,2KAAA,CAAA,cAAW;;;KAjE3C", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/components/FilePreview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Separator } from \"@/components/ui/separator\";\n\ninterface FileInfo {\n  file_id: string;\n  original_filename: string;\n  file_size: number;\n  file_type: string;\n  character_count: number;\n  line_count: number;\n  text_preview: string;\n  upload_time: string;\n}\n\ninterface FilePreviewProps {\n  fileInfo: FileInfo;\n  onUseFile: (content: string) => void;\n  onRemoveFile: () => void;\n  onEditContent: (content: string) => void;\n}\n\nexport default function FilePreview({ \n  fileInfo, \n  onUseFile, \n  onRemoveFile, \n  onEditContent \n}: FilePreviewProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  const [editedContent, setEditedContent] = useState(\"\");\n  const [fullContent, setFullContent] = useState<string | null>(null);\n  const [isLoadingContent, setIsLoadingContent] = useState(false);\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleString('zh-CN');\n  };\n\n  const loadFullContent = async () => {\n    if (fullContent) {\n      setIsExpanded(!isExpanded);\n      return;\n    }\n\n    setIsLoadingContent(true);\n    try {\n      const response = await fetch(`http://localhost:8002/api/v1/file/${fileInfo.file_id}`);\n      if (response.ok) {\n        const data = await response.json();\n        setFullContent(data.text_content);\n        setEditedContent(data.text_content);\n        setIsExpanded(true);\n      } else {\n        console.error('Failed to load file content');\n      }\n    } catch (error) {\n      console.error('Error loading file content:', error);\n    } finally {\n      setIsLoadingContent(false);\n    }\n  };\n\n  const handleEdit = () => {\n    setIsEditing(true);\n  };\n\n  const handleSaveEdit = () => {\n    setFullContent(editedContent);\n    onEditContent(editedContent);\n    setIsEditing(false);\n  };\n\n  const handleCancelEdit = () => {\n    setEditedContent(fullContent || \"\");\n    setIsEditing(false);\n  };\n\n  const handleUseFile = () => {\n    const contentToUse = fullContent || fileInfo.text_preview;\n    onUseFile(contentToUse);\n  };\n\n  return (\n    <Card className=\"border-green-200 bg-green-50\">\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <span className=\"text-green-600\">\n                {fileInfo.file_type === 'txt' ? '📄' : '📝'}\n              </span>\n              {fileInfo.original_filename}\n            </CardTitle>\n            <CardDescription>\n              上传时间: {formatDate(fileInfo.upload_time)}\n            </CardDescription>\n          </div>\n          <Button \n            variant=\"outline\" \n            size=\"sm\" \n            onClick={onRemoveFile}\n            className=\"text-red-600 hover:text-red-700\"\n          >\n            移除\n          </Button>\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-4\">\n        {/* 文件信息 */}\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <Label className=\"text-gray-600\">文件大小</Label>\n            <p className=\"font-medium\">{formatFileSize(fileInfo.file_size)}</p>\n          </div>\n          <div>\n            <Label className=\"text-gray-600\">文件类型</Label>\n            <p className=\"font-medium\">{fileInfo.file_type.toUpperCase()}</p>\n          </div>\n          <div>\n            <Label className=\"text-gray-600\">字符数</Label>\n            <p className=\"font-medium\">{fileInfo.character_count.toLocaleString()}</p>\n          </div>\n          <div>\n            <Label className=\"text-gray-600\">行数</Label>\n            <p className=\"font-medium\">{fileInfo.line_count.toLocaleString()}</p>\n          </div>\n        </div>\n\n        <Separator />\n\n        {/* 内容预览 */}\n        <div>\n          <div className=\"flex items-center justify-between mb-2\">\n            <Label>内容预览</Label>\n            <div className=\"space-x-2\">\n              <Button \n                variant=\"outline\" \n                size=\"sm\" \n                onClick={loadFullContent}\n                disabled={isLoadingContent}\n              >\n                {isLoadingContent ? \"加载中...\" : isExpanded ? \"收起\" : \"展开全文\"}\n              </Button>\n              {isExpanded && !isEditing && (\n                <Button variant=\"outline\" size=\"sm\" onClick={handleEdit}>\n                  编辑\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {isExpanded ? (\n            <div className=\"space-y-2\">\n              {isEditing ? (\n                <>\n                  <Textarea\n                    value={editedContent}\n                    onChange={(e) => setEditedContent(e.target.value)}\n                    className=\"min-h-[300px] resize-none\"\n                    placeholder=\"编辑文件内容...\"\n                  />\n                  <div className=\"flex space-x-2\">\n                    <Button size=\"sm\" onClick={handleSaveEdit}>\n                      保存\n                    </Button>\n                    <Button variant=\"outline\" size=\"sm\" onClick={handleCancelEdit}>\n                      取消\n                    </Button>\n                  </div>\n                </>\n              ) : (\n                <div className=\"p-3 bg-white border rounded-md max-h-[300px] overflow-y-auto whitespace-pre-wrap text-sm\">\n                  {fullContent || \"加载中...\"}\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"p-3 bg-white border rounded-md\">\n              <p className=\"text-sm text-gray-700 whitespace-pre-wrap\">\n                {fileInfo.text_preview}\n              </p>\n              {fileInfo.character_count > 200 && (\n                <p className=\"text-xs text-gray-500 mt-2\">\n                  ... 还有 {fileInfo.character_count - 200} 个字符\n                </p>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex space-x-2 pt-2\">\n          <Button onClick={handleUseFile} className=\"flex-1\">\n            🚀 使用此文件进行转换\n          </Button>\n          <Button variant=\"outline\" onClick={() => window.open(`http://localhost:8002/api/v1/file/${fileInfo.file_id}`)}>\n            📥 下载原文件\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA2Be,SAAS,YAAY,EAClC,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,aAAa,EACI;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC;IAC7C;IAEA,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,cAAc,CAAC;YACf;QACF;QAEA,oBAAoB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kCAAkC,EAAE,SAAS,OAAO,EAAE;YACpF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,KAAK,YAAY;gBAChC,iBAAiB,KAAK,YAAY;gBAClC,cAAc;YAChB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,eAAe;QACf,cAAc;QACd,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,iBAAiB,eAAe;QAChC,aAAa;IACf;IAEA,MAAM,gBAAgB;QACpB,MAAM,eAAe,eAAe,SAAS,YAAY;QACzD,UAAU;IACZ;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC;4CAAK,WAAU;sDACb,SAAS,SAAS,KAAK,QAAQ,OAAO;;;;;;wCAExC,SAAS,iBAAiB;;;;;;;8CAE7B,6LAAC,mIAAA,CAAA,kBAAe;;wCAAC;wCACR,WAAW,SAAS,WAAW;;;;;;;;;;;;;sCAG1C,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAML,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAgB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAAe,eAAe,SAAS,SAAS;;;;;;;;;;;;0CAE/D,6LAAC;;kDACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAgB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAAe,SAAS,SAAS,CAAC,WAAW;;;;;;;;;;;;0CAE5D,6LAAC;;kDACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAgB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAAe,SAAS,eAAe,CAAC,cAAc;;;;;;;;;;;;0CAErE,6LAAC;;kDACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAgB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAAe,SAAS,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;kCAIlE,6LAAC,wIAAA,CAAA,YAAS;;;;;kCAGV,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU;0DAET,mBAAmB,WAAW,aAAa,OAAO;;;;;;4CAEpD,cAAc,CAAC,2BACd,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,SAAS;0DAAY;;;;;;;;;;;;;;;;;;4BAO9D,2BACC,6LAAC;gCAAI,WAAU;0CACZ,0BACC;;sDACE,6LAAC,uIAAA,CAAA,WAAQ;4CACP,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;4CACV,aAAY;;;;;;sDAEd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAS;8DAAgB;;;;;;8DAG3C,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,SAAS;8DAAkB;;;;;;;;;;;;;iEAMnE,6LAAC;oCAAI,WAAU;8CACZ,eAAe;;;;;;;;;;qDAKtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,SAAS,YAAY;;;;;;oCAEvB,SAAS,eAAe,GAAG,qBAC1B,6LAAC;wCAAE,WAAU;;4CAA6B;4CAChC,SAAS,eAAe,GAAG;4CAAI;;;;;;;;;;;;;;;;;;;kCAQjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAe,WAAU;0CAAS;;;;;;0CAGnD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,kCAAkC,EAAE,SAAS,OAAO,EAAE;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAOzH;GA7LwB;KAAA", "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/3.Cursor/1.Record%20Transfer/Test15%20%E9%9C%80%E6%B1%82%E6%95%B4%E7%90%86_%E5%89%AF%E6%9C%AC/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Separator } from \"@/components/ui/separator\";\nimport Link from \"next/link\";\nimport FileUpload from \"@/components/FileUpload\";\nimport FilePreview from \"@/components/FilePreview\";\n\ninterface TransformResult {\n  id: number;\n  original_text: string;\n  transformed_text: string;\n  status: string;\n  metrics?: {\n    word_retention_rate: number;\n    quote_retention_rate: number;\n    content_preservation_rate: number;\n  };\n  error_message?: string;\n}\n\ninterface FileInfo {\n  file_id: string;\n  original_filename: string;\n  file_size: number;\n  file_type: string;\n  character_count: number;\n  line_count: number;\n  text_preview: string;\n  upload_time: string;\n}\n\nexport default function Dashboard() {\n  const [inputText, setInputText] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [result, setResult] = useState<TransformResult | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const [uploadedFile, setUploadedFile] = useState<FileInfo | null>(null);\n  const [inputMode, setInputMode] = useState<\"text\" | \"file\">(\"text\");\n\n  const handleTransform = async () => {\n    if (!inputText.trim()) {\n      setError(\"请输入要转换的文本\");\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const response = await fetch(\"http://localhost:8002/api/v1/transform\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          text: inputText,\n          rules: [\"speaker_identification\", \"language_optimization\"],\n          llm_provider: \"deepseek\"\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`转换失败: ${response.status}`);\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"转换过程中发生错误\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setInputText(\"\");\n    setResult(null);\n    setError(null);\n    setUploadedFile(null);\n    setInputMode(\"text\");\n  };\n\n  const handleFileUploaded = (fileInfo: FileInfo) => {\n    setUploadedFile(fileInfo);\n    setInputMode(\"file\");\n    setError(null);\n  };\n\n  const handleFileError = (errorMessage: string) => {\n    setError(errorMessage);\n    setUploadedFile(null);\n  };\n\n  const handleUseFile = (content: string) => {\n    setInputText(content);\n    setInputMode(\"text\");\n  };\n\n  const handleRemoveFile = () => {\n    setUploadedFile(null);\n    setInputMode(\"text\");\n  };\n\n  const handleEditContent = (content: string) => {\n    setInputText(content);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                📝 笔录转换系统\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-500\">v0.1.0-alpha</span>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主要内容 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* 左侧：输入区域 */}\n          <div className=\"space-y-6\">\n            {/* 输入模式切换 */}\n            <Card>\n              <CardHeader>\n                <CardTitle>📝 输入方式选择</CardTitle>\n                <CardDescription>\n                  选择文本输入或文件上传方式\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex space-x-4\">\n                  <Button\n                    variant={inputMode === \"text\" ? \"default\" : \"outline\"}\n                    onClick={() => setInputMode(\"text\")}\n                    className=\"flex-1\"\n                  >\n                    ✏️ 文本输入\n                  </Button>\n                  <Button\n                    variant={inputMode === \"file\" ? \"default\" : \"outline\"}\n                    onClick={() => setInputMode(\"file\")}\n                    className=\"flex-1\"\n                  >\n                    📁 文件上传\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* 文件上传区域 */}\n            {inputMode === \"file\" && !uploadedFile && (\n              <FileUpload\n                onFileUploaded={handleFileUploaded}\n                onError={handleFileError}\n              />\n            )}\n\n            {/* 文件预览区域 */}\n            {uploadedFile && (\n              <FilePreview\n                fileInfo={uploadedFile}\n                onUseFile={handleUseFile}\n                onRemoveFile={handleRemoveFile}\n                onEditContent={handleEditContent}\n              />\n            )}\n\n            {/* 文本输入区域 */}\n            {inputMode === \"text\" && (\n              <Card>\n                <CardHeader>\n                  <CardTitle>📝 原始笔录输入</CardTitle>\n                  <CardDescription>\n                    请输入或粘贴需要转换的访谈笔录内容\n                  </CardDescription>\n                </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"input-text\">笔录内容</Label>\n                  <Textarea\n                    id=\"input-text\"\n                    placeholder=\"请输入访谈笔录内容，例如：&#10;M：主任您好，华为委托的第三方...&#10;1：好，您说。&#10;M：想了解一下我们跟华为合作的使用它的产品方案...\"\n                    value={inputText}\n                    onChange={(e) => setInputText(e.target.value)}\n                    className=\"min-h-[300px] resize-none\"\n                  />\n                  <div className=\"text-sm text-gray-500 mt-2\">\n                    字数: {inputText.length} / 50000\n                  </div>\n                </div>\n\n                <div className=\"flex space-x-4\">\n                  <Button \n                    onClick={handleTransform} \n                    disabled={isLoading || !inputText.trim()}\n                    className=\"flex-1\"\n                  >\n                    {isLoading ? \"转换中...\" : \"🚀 开始转换\"}\n                  </Button>\n                  <Button \n                    variant=\"outline\" \n                    onClick={handleReset}\n                    disabled={isLoading}\n                  >\n                    🔄 重置\n                  </Button>\n                </div>\n\n                {error && (\n                  <div className=\"p-4 bg-red-50 border border-red-200 rounded-md\">\n                    <p className=\"text-red-600 text-sm\">{error}</p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n            )}\n\n            {/* 规则配置区域 */}\n            <Card>\n              <CardHeader>\n                <CardTitle>⚙️ 转换规则配置</CardTitle>\n                <CardDescription>\n                  当前使用默认规则集\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-sm\">发言人识别</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-sm\">语言优化</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-sm\">视角转换</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* 右侧：结果区域 */}\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>📄 转换结果</CardTitle>\n                <CardDescription>\n                  转换后的第一人称叙述文档\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                {result ? (\n                  <div className=\"space-y-4\">\n                    <div>\n                      <Label>转换后内容</Label>\n                      <div className=\"mt-2 p-4 bg-gray-50 border rounded-md min-h-[300px] whitespace-pre-wrap\">\n                        {result.transformed_text || \"转换中...\"}\n                      </div>\n                    </div>\n\n                    {result.status === \"completed\" && (\n                      <div className=\"flex space-x-2\">\n                        <Button size=\"sm\" variant=\"outline\">\n                          📥 下载 TXT\n                        </Button>\n                        <Button size=\"sm\" variant=\"outline\">\n                          📥 下载 DOCX\n                        </Button>\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"flex items-center justify-center h-[300px] text-gray-500\">\n                    <div className=\"text-center\">\n                      <div className=\"text-4xl mb-4\">📝</div>\n                      <p>转换结果将在这里显示</p>\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* 质量指标 */}\n            {result?.metrics && (\n              <Card>\n                <CardHeader>\n                  <CardTitle>📊 质量指标</CardTitle>\n                  <CardDescription>\n                    转换质量评估结果\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span>字数保留率</span>\n                        <span>{(result.metrics.word_retention_rate * 100).toFixed(1)}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2 mt-1\">\n                        <div \n                          className=\"bg-blue-600 h-2 rounded-full\" \n                          style={{ width: `${result.metrics.word_retention_rate * 100}%` }}\n                        ></div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span>原文引用率</span>\n                        <span>{(result.metrics.quote_retention_rate * 100).toFixed(1)}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2 mt-1\">\n                        <div \n                          className=\"bg-green-600 h-2 rounded-full\" \n                          style={{ width: `${result.metrics.quote_retention_rate * 100}%` }}\n                        ></div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span>实质保持率</span>\n                        <span>{(result.metrics.content_preservation_rate * 100).toFixed(1)}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2 mt-1\">\n                        <div \n                          className=\"bg-purple-600 h-2 rounded-full\" \n                          style={{ width: `${result.metrics.content_preservation_rate * 100}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAVA;;;;;;;;;AAoCe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE5D,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,0CAA0C;gBACrE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,OAAO;wBAAC;wBAA0B;qBAAwB;oBAC1D,cAAc;gBAChB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,SAAS,MAAM,EAAE;YAC5C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,UAAU;QACV,SAAS;QACT,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;QAChB,aAAa;QACb,SAAS;IACX;IAEA,MAAM,kBAAkB,CAAC;QACvB,SAAS;QACT,gBAAgB;IAClB;IAEA,MAAM,gBAAgB,CAAC;QACrB,aAAa;QACb,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC;QACzB,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAI7D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,cAAc,SAAS,YAAY;wDAC5C,SAAS,IAAM,aAAa;wDAC5B,WAAU;kEACX;;;;;;kEAGD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,cAAc,SAAS,YAAY;wDAC5C,SAAS,IAAM,aAAa;wDAC5B,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;gCAQN,cAAc,UAAU,CAAC,8BACxB,6LAAC,mIAAA,CAAA,UAAU;oCACT,gBAAgB;oCAChB,SAAS;;;;;;gCAKZ,8BACC,6LAAC,oIAAA,CAAA,UAAW;oCACV,UAAU;oCACV,WAAW;oCACX,cAAc;oCACd,eAAe;;;;;;gCAKlB,cAAc,wBACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAIrB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa;;;;;;sEAC5B,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;gEAA6B;gEACrC,UAAU,MAAM;gEAAC;;;;;;;;;;;;;8DAI1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU,aAAa,CAAC,UAAU,IAAI;4DACtC,WAAU;sEAET,YAAY,WAAW;;;;;;sEAE1B,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS;4DACT,UAAU;sEACX;;;;;;;;;;;;gDAKF,uBACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;8CAQ7C,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACT,uBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,6LAAC;gEAAI,WAAU;0EACZ,OAAO,gBAAgB,IAAI;;;;;;;;;;;;oDAI/B,OAAO,MAAM,KAAK,6BACjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAK,SAAQ;0EAAU;;;;;;0EAGpC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAK,SAAQ;0EAAU;;;;;;;;;;;;;;;;;qEAO1C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQZ,QAAQ,yBACP,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;;4EAAM,CAAC,OAAO,OAAO,CAAC,mBAAmB,GAAG,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE/D,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,OAAO,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC;oEAAC;;;;;;;;;;;;;;;;;kEAKrE,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;;4EAAM,CAAC,OAAO,OAAO,CAAC,oBAAoB,GAAG,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAEhE,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,OAAO,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;oEAAC;;;;;;;;;;;;;;;;;kEAKtE,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;;4EAAM,CAAC,OAAO,OAAO,CAAC,yBAAyB,GAAG,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAErE,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,OAAO,OAAO,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAC;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa/F;GAlUwB;KAAA", "debugId": null}}]}