"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestPage() {
  const [result, setResult] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const testAPI = async () => {
    setLoading(true);
    setResult("");
    
    try {
      // 测试健康检查
      console.log("测试健康检查...");
      const healthResponse = await fetch("http://localhost:8002/api/v1/health", {
        mode: 'cors',
      });
      
      if (!healthResponse.ok) {
        throw new Error(`健康检查失败: ${healthResponse.status}`);
      }
      
      const healthData = await healthResponse.json();
      console.log("健康检查成功:", healthData);
      
      // 测试文本转换
      console.log("测试文本转换...");
      const transformResponse = await fetch("http://localhost:8002/api/v1/transform", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        mode: 'cors',
        body: JSON.stringify({
          text: "M：您好\n1：好的，我很好",
          rules: [],
          llm_provider: "mock"
        }),
      });
      
      if (!transformResponse.ok) {
        throw new Error(`文本转换失败: ${transformResponse.status}`);
      }
      
      const transformData = await transformResponse.json();
      console.log("文本转换成功:", transformData);
      
      setResult(`✅ 所有测试通过！
      
健康检查: ${healthData.status} (${healthData.version})
文本转换: ${transformData.status}
转换结果: ${transformData.transformed_text.substring(0, 100)}...`);
      
    } catch (error) {
      console.error("测试失败:", error);
      setResult(`❌ 测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const testFileUpload = async () => {
    setLoading(true);
    setResult("");
    
    try {
      // 创建测试文件
      const testContent = "M：主任您好，华为委托的第三方\n1：好，您说。";
      const blob = new Blob([testContent], { type: 'text/plain' });
      const file = new File([blob], 'test.txt', { type: 'text/plain' });
      
      const formData = new FormData();
      formData.append('file', file);
      
      console.log("测试文件上传...");
      const response = await fetch('http://localhost:8002/api/v1/file/upload', {
        method: 'POST',
        body: formData,
        mode: 'cors',
      });
      
      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`文件上传失败: ${response.status} - ${errorData}`);
      }
      
      const fileData = await response.json();
      console.log("文件上传成功:", fileData);
      
      setResult(`✅ 文件上传测试通过！
      
文件ID: ${fileData.file_id}
文件名: ${fileData.original_filename}
文件大小: ${fileData.file_size} bytes
字符数: ${fileData.character_count}
预览: ${fileData.text_preview}`);
      
    } catch (error) {
      console.error("文件上传测试失败:", error);
      setResult(`❌ 文件上传测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>🧪 API测试页面</CardTitle>
            <CardDescription>
              测试后端API连接和功能
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-4">
              <Button 
                onClick={testAPI} 
                disabled={loading}
                className="flex-1"
              >
                {loading ? "测试中..." : "测试基础API"}
              </Button>
              <Button 
                onClick={testFileUpload} 
                disabled={loading}
                variant="outline"
                className="flex-1"
              >
                {loading ? "测试中..." : "测试文件上传"}
              </Button>
            </div>
            
            {result && (
              <div className="mt-6 p-4 bg-gray-100 rounded-lg">
                <h3 className="font-semibold mb-2">测试结果:</h3>
                <pre className="text-sm whitespace-pre-wrap">{result}</pre>
              </div>
            )}
            
            <div className="mt-6 text-sm text-gray-600">
              <p><strong>说明:</strong></p>
              <ul className="list-disc list-inside space-y-1">
                <li>此页面用于测试后端API连接</li>
                <li>如果测试失败，请检查后端服务是否运行在 http://localhost:8002</li>
                <li>打开浏览器开发者工具查看详细错误信息</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
