"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import FileUpload from "@/components/FileUpload";

interface TransformResult {
  id: number;
  original_text: string;
  transformed_text: string;
  status: string;
  metrics?: {
    word_retention_rate: number;
    quote_retention_rate: number;
    content_preservation_rate: number;
  };
  error_message?: string;
}

interface FileInfo {
  file_id: string;
  original_filename: string;
  file_size: number;
  file_type: string;
  character_count: number;
  line_count: number;
  text_preview: string;
  upload_time: string;
}

const defaultTestText = `M：主任您好，华为委托的第三方。
1：好，您说。
M：想了解一下我们跟华为合作的使用它的产品方案。
1：嗯，得有一二十年了，基本上用了它网络安全什么服务器什么一类的，基本上全用了，这几大品类全用了。
M：那您觉得华为的产品质量怎么样？
1：啊，我觉得还是不错的，技术比较先进，华为的售前特别专业，这一块应该是全国数一数二的。
M：有什么需要改进的地方吗？
1：价格相较其他要贵不少，我们采购的话确实是有点不太好中标。`;

export default function Dashboard() {
  const [inputText, setInputText] = useState(defaultTestText);
  const [outputText, setOutputText] = useState("");
  const [isTransforming, setIsTransforming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<FileInfo | null>(null);
  const [inputMode, setInputMode] = useState<"text" | "file">("text");
  const [currentRuleSet, setCurrentRuleSet] = useState<any>(null);
  const [savedRuleSets, setSavedRuleSets] = useState<any[]>([]);
  const [selectedRuleSetId, setSelectedRuleSetId] = useState<string>("default_ruleset");

  // 加载规则集
  useEffect(() => {
    const loadRuleSets = async () => {
      try {
        // 加载当前规则集
        const currentResponse = await fetch("http://localhost:8002/api/v1/simple-rules/current", {
          mode: 'cors',
        });
        if (currentResponse.ok) {
          const current = await currentResponse.json();
          setCurrentRuleSet(current);
        }

        // 加载已保存的规则集
        const savedResponse = await fetch("http://localhost:8002/api/v1/simple-rules/saved", {
          mode: 'cors',
        });
        if (savedResponse.ok) {
          const saved = await savedResponse.json();
          setSavedRuleSets(saved);
        }
      } catch (err) {
        console.error("加载规则集失败:", err);
      }
    };

    loadRuleSets();
  }, []);

  const handleTransform = async () => {
    if (!inputText.trim()) {
      setError("请输入要转换的文本");
      return;
    }

    setIsTransforming(true);
    setError(null);
    setOutputText("");

    try {
      const response = await fetch("http://localhost:8002/api/v1/transform", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        mode: 'cors',
        body: JSON.stringify({
          text: inputText,
          ruleset_id: selectedRuleSetId,
          llm_provider: "deepseek"
        }),
      });

      if (!response.ok) {
        throw new Error(`转换失败: ${response.status}`);
      }

      const data = await response.json();
      setOutputText(data.transformed_text || data.result || "转换完成");
    } catch (err) {
      setError(err instanceof Error ? err.message : "转换过程中发生错误");
    } finally {
      setIsTransforming(false);
    }
  };

  // 切换规则集
  const switchRuleSet = async (ruleSetId: string) => {
    try {
      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/switch/${ruleSetId}`, {
        method: 'POST',
        mode: 'cors',
      });

      if (response.ok) {
        setSelectedRuleSetId(ruleSetId);
        // 重新加载当前规则集
        const currentResponse = await fetch("http://localhost:8002/api/v1/simple-rules/current", {
          mode: 'cors',
        });
        if (currentResponse.ok) {
          const current = await currentResponse.json();
          setCurrentRuleSet(current);
        }
      }
    } catch (err) {
      setError("切换规则集失败");
    }
  };

  const handleReset = () => {
    setInputText("");
    setOutputText("");
    setError(null);
    setUploadedFile(null);
    setInputMode("text");
  };

  const handleFileUploaded = (fileInfo: FileInfo) => {
    setUploadedFile(fileInfo);
    setInputMode("file");
    setError(null);
  };

  const handleFileError = (errorMessage: string) => {
    setError(errorMessage);
    setUploadedFile(null);
  };

  const handleUseFile = (content: string) => {
    setInputText(content);
    setInputMode("text");
  };

  const handleRemoveFile = () => {
    setUploadedFile(null);
    setInputMode("text");
  };

  const handleEditContent = (content: string) => {
    setInputText(content);
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                📝 笔录转换系统
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/rules">
                <Button variant="outline">⚙️ 规则管理</Button>
              </Link>
              <span className="text-sm text-gray-500">v0.2.0-beta</span>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 - 上中下布局 */}
      <main className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 flex flex-col space-y-4">
        {/* 错误提示 */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* 上部：上传或粘贴 */}
        <Card className="flex-shrink-0">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">📝 输入笔录</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-4">
              <div className="flex space-x-4">
                <Button
                  variant={inputMode === "text" ? "default" : "outline"}
                  onClick={() => setInputMode("text")}
                  size="sm"
                >
                  ✏️ 文本输入
                </Button>
                <Button
                  variant={inputMode === "file" ? "default" : "outline"}
                  onClick={() => setInputMode("file")}
                  size="sm"
                >
                  📁 文件上传
                </Button>
              </div>

              {inputMode === "text" && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setInputText(defaultTestText)}
                >
                  📝 使用示例
                </Button>
              )}
            </div>

            {inputMode === "file" && !uploadedFile && (
              <FileUpload
                onFileUploaded={handleFileUploaded}
                onError={handleFileError}
              />
            )}

            {uploadedFile && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-sm">{uploadedFile.original_filename}</div>
                    <div className="text-xs text-gray-600">
                      {uploadedFile.file_size} bytes • {uploadedFile.character_count} 字符
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" onClick={() => handleUseFile(uploadedFile.text_preview)}>
                      使用
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleRemoveFile}>
                      移除
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {inputMode === "text" && (
              <Textarea
                placeholder="请输入或粘贴访谈笔录内容..."
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                className="min-h-[120px] resize-none"
              />
            )}
          </CardContent>
        </Card>

        {/* 中部：选择转换规则集 */}
        <Card className="flex-shrink-0">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">⚙️ 转换规则配置</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
              <div>
                <Label className="text-sm font-medium mb-2 block">当前规则集</Label>
                <Select value={selectedRuleSetId} onValueChange={switchRuleSet}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {savedRuleSets.map((ruleSet) => (
                      <SelectItem key={ruleSet.id} value={ruleSet.id}>
                        {ruleSet.name} {ruleSet.is_default && "(默认)"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex space-x-2">
                <Link href="/rules">
                  <Button variant="outline" size="sm">
                    📝 详细配置
                  </Button>
                </Link>
                <Button
                  onClick={handleTransform}
                  disabled={isTransforming || !inputText.trim()}
                  size="sm"
                >
                  {isTransforming ? "转换中..." : "🚀 开始转换"}
                </Button>
              </div>

              <div className="text-sm text-gray-600">
                {currentRuleSet && (
                  <div>
                    <div>规则大类: {currentRuleSet.categories?.length || 0}</div>
                    <div>总规则数: {currentRuleSet.categories?.reduce((total: number, cat: any) =>
                      total + cat.main_rules?.reduce((subTotal: number, rule: any) =>
                        subTotal + (rule.sub_rules?.length || 0), 0), 0) || 0}</div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 下部：左右对比 */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-4 min-h-0">
          {/* 左侧：原始笔录 */}
          <Card className="flex flex-col">
            <CardHeader className="pb-3 flex-shrink-0">
              <CardTitle className="text-lg">📄 原始笔录</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col min-h-0">
              {inputText ? (
                <div className="flex-1 p-3 bg-gray-50 border rounded-md overflow-y-auto whitespace-pre-wrap text-sm">
                  {inputText}
                </div>
              ) : (
                <div className="flex-1 flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <div className="text-4xl mb-2">📝</div>
                    <p className="text-sm">原始笔录将在这里显示</p>
                  </div>
                </div>
              )}
              <div className="text-xs text-gray-500 mt-2 flex-shrink-0">
                字符数: {inputText.length}
              </div>
            </CardContent>
          </Card>

          {/* 右侧：转换后笔录 */}
          <Card className="flex flex-col">
            <CardHeader className="pb-3 flex-shrink-0">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">📋 转换结果</CardTitle>
                {outputText && (
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      📥 下载
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleReset}>
                      🔄 重置
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col min-h-0">
              {isTransforming ? (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-4xl mb-2">⏳</div>
                    <p className="text-sm">正在转换中...</p>
                  </div>
                </div>
              ) : outputText ? (
                <div className="flex-1 p-3 bg-blue-50 border rounded-md overflow-y-auto whitespace-pre-wrap text-sm">
                  {outputText}
                </div>
              ) : (
                <div className="flex-1 flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <div className="text-4xl mb-2">📋</div>
                    <p className="text-sm">转换结果将在这里显示</p>
                  </div>
                </div>
              )}
              {outputText && (
                <div className="text-xs text-gray-500 mt-2 flex-shrink-0">
                  字符数: {outputText.length}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
