"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import FileUpload from "@/components/FileUpload";
import FilePreview from "@/components/FilePreview";

interface TransformResult {
  id: number;
  original_text: string;
  transformed_text: string;
  status: string;
  metrics?: {
    word_retention_rate: number;
    quote_retention_rate: number;
    content_preservation_rate: number;
  };
  error_message?: string;
}

interface FileInfo {
  file_id: string;
  original_filename: string;
  file_size: number;
  file_type: string;
  character_count: number;
  line_count: number;
  text_preview: string;
  upload_time: string;
}

export default function Dashboard() {
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<TransformResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<FileInfo | null>(null);
  const [inputMode, setInputMode] = useState<"text" | "file">("text");

  const handleTransform = async () => {
    if (!inputText.trim()) {
      setError("请输入要转换的文本");
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch("http://localhost:8002/api/v1/transform", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        mode: 'cors',
        body: JSON.stringify({
          text: inputText,
          rules: ["speaker_identification", "language_optimization"],
          llm_provider: "deepseek"
        }),
      });

      if (!response.ok) {
        throw new Error(`转换失败: ${response.status}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "转换过程中发生错误");
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setInputText("");
    setResult(null);
    setError(null);
    setUploadedFile(null);
    setInputMode("text");
  };

  const handleFileUploaded = (fileInfo: FileInfo) => {
    setUploadedFile(fileInfo);
    setInputMode("file");
    setError(null);
  };

  const handleFileError = (errorMessage: string) => {
    setError(errorMessage);
    setUploadedFile(null);
  };

  const handleUseFile = (content: string) => {
    setInputText(content);
    setInputMode("text");
  };

  const handleRemoveFile = () => {
    setUploadedFile(null);
    setInputMode("text");
  };

  const handleEditContent = (content: string) => {
    setInputText(content);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                📝 笔录转换系统
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">v0.1.0-alpha</span>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：输入区域 */}
          <div className="space-y-6">
            {/* 输入模式切换 */}
            <Card>
              <CardHeader>
                <CardTitle>📝 输入方式选择</CardTitle>
                <CardDescription>
                  选择文本输入或文件上传方式
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex space-x-4">
                  <Button
                    variant={inputMode === "text" ? "default" : "outline"}
                    onClick={() => setInputMode("text")}
                    className="flex-1"
                  >
                    ✏️ 文本输入
                  </Button>
                  <Button
                    variant={inputMode === "file" ? "default" : "outline"}
                    onClick={() => setInputMode("file")}
                    className="flex-1"
                  >
                    📁 文件上传
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 文件上传区域 */}
            {inputMode === "file" && !uploadedFile && (
              <FileUpload
                onFileUploaded={handleFileUploaded}
                onError={handleFileError}
              />
            )}

            {/* 文件预览区域 */}
            {uploadedFile && (
              <FilePreview
                fileInfo={uploadedFile}
                onUseFile={handleUseFile}
                onRemoveFile={handleRemoveFile}
                onEditContent={handleEditContent}
              />
            )}

            {/* 文本输入区域 */}
            {inputMode === "text" && (
              <Card>
                <CardHeader>
                  <CardTitle>📝 原始笔录输入</CardTitle>
                  <CardDescription>
                    请输入或粘贴需要转换的访谈笔录内容
                  </CardDescription>
                </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="input-text">笔录内容</Label>
                  <Textarea
                    id="input-text"
                    placeholder="请输入访谈笔录内容，例如：&#10;M：主任您好，华为委托的第三方...&#10;1：好，您说。&#10;M：想了解一下我们跟华为合作的使用它的产品方案..."
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    className="min-h-[300px] resize-none"
                  />
                  <div className="text-sm text-gray-500 mt-2">
                    字数: {inputText.length} / 50000
                  </div>
                </div>

                <div className="flex space-x-4">
                  <Button 
                    onClick={handleTransform} 
                    disabled={isLoading || !inputText.trim()}
                    className="flex-1"
                  >
                    {isLoading ? "转换中..." : "🚀 开始转换"}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={handleReset}
                    disabled={isLoading}
                  >
                    🔄 重置
                  </Button>
                </div>

                {error && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-red-600 text-sm">{error}</p>
                  </div>
                )}
              </CardContent>
            </Card>
            )}

            {/* 规则配置区域 */}
            <Card>
              <CardHeader>
                <CardTitle>⚙️ 转换规则配置</CardTitle>
                <CardDescription>
                  当前使用默认规则集
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">发言人识别</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">语言优化</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">视角转换</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：结果区域 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>📄 转换结果</CardTitle>
                <CardDescription>
                  转换后的第一人称叙述文档
                </CardDescription>
              </CardHeader>
              <CardContent>
                {result ? (
                  <div className="space-y-4">
                    <div>
                      <Label>转换后内容</Label>
                      <div className="mt-2 p-4 bg-gray-50 border rounded-md min-h-[300px] whitespace-pre-wrap">
                        {result.transformed_text || "转换中..."}
                      </div>
                    </div>

                    {result.status === "completed" && (
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          📥 下载 TXT
                        </Button>
                        <Button size="sm" variant="outline">
                          📥 下载 DOCX
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-gray-500">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📝</div>
                      <p>转换结果将在这里显示</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 质量指标 */}
            {result?.metrics && (
              <Card>
                <CardHeader>
                  <CardTitle>📊 质量指标</CardTitle>
                  <CardDescription>
                    转换质量评估结果
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>字数保留率</span>
                        <span>{(result.metrics.word_retention_rate * 100).toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${result.metrics.word_retention_rate * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm">
                        <span>原文引用率</span>
                        <span>{(result.metrics.quote_retention_rate * 100).toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${result.metrics.quote_retention_rate * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm">
                        <span>实质保持率</span>
                        <span>{(result.metrics.content_preservation_rate * 100).toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ width: `${result.metrics.content_preservation_rate * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
