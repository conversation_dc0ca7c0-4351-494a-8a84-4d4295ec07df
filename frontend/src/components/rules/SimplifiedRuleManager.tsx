"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import RuleTestModal from "./RuleTestModal";

interface RuleExample {
  input_text: string;
  output_text: string;
  description: string;
}

interface SubRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  implementation_type: string;
  examples: RuleExample[];
  test_cases: string[];
}

interface MainRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  sub_rules: SubRule[];
}

interface RuleCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  main_rules: MainRule[];
}

interface RuleSet {
  id: string;
  name: string;
  description: string;
  is_default: boolean;
  categories: RuleCategory[];
}

interface SavedRuleSet {
  id: string;
  name: string;
  description: string;
  is_default: boolean;
}

export default function SimplifiedRuleManager() {
  const [currentRuleSet, setCurrentRuleSet] = useState<RuleSet | null>(null);
  const [savedRuleSets, setSavedRuleSets] = useState<SavedRuleSet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [expandedMainRules, setExpandedMainRules] = useState<Set<string>>(new Set());
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [newRuleSetName, setNewRuleSetName] = useState("");
  const [newRuleSetDescription, setNewRuleSetDescription] = useState("");
  const [testingSubRule, setTestingSubRule] = useState<SubRule | null>(null);
  const [statistics, setStatistics] = useState<any>(null);

  // 加载当前规则集
  const loadCurrentRuleSet = async () => {
    try {
      const response = await fetch("http://localhost:8002/api/v1/simple-rules/current", {
        mode: 'cors',
      });
      
      if (!response.ok) {
        throw new Error(`加载规则集失败: ${response.status}`);
      }
      
      const data = await response.json();
      setCurrentRuleSet(data);
      
      // 默认展开第一个分类
      if (data.categories.length > 0) {
        setExpandedCategories(new Set([data.categories[0].id]));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "加载规则集失败");
    }
  };

  // 加载已保存的规则集
  const loadSavedRuleSets = async () => {
    try {
      const response = await fetch("http://localhost:8002/api/v1/simple-rules/saved", {
        mode: 'cors',
      });
      
      if (!response.ok) {
        throw new Error(`加载已保存规则集失败: ${response.status}`);
      }
      
      const data = await response.json();
      setSavedRuleSets(data);
    } catch (err) {
      console.error("加载已保存规则集失败:", err);
    }
  };

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await fetch("http://localhost:8002/api/v1/simple-rules/statistics", {
        mode: 'cors',
      });
      
      if (response.ok) {
        const data = await response.json();
        setStatistics(data);
      }
    } catch (err) {
      console.error("加载统计信息失败:", err);
    }
  };

  // 初始化加载
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        loadCurrentRuleSet(),
        loadSavedRuleSets(),
        loadStatistics()
      ]);
      setLoading(false);
    };
    
    loadData();
  }, []);

  // 切换规则集
  const switchRuleSet = async (ruleSetId: string) => {
    try {
      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/switch/${ruleSetId}`, {
        method: 'POST',
        mode: 'cors',
      });
      
      if (!response.ok) {
        throw new Error(`切换规则集失败: ${response.status}`);
      }
      
      await loadCurrentRuleSet();
      await loadStatistics();
    } catch (err) {
      setError(err instanceof Error ? err.message : "切换规则集失败");
    }
  };

  // 保存当前规则集
  const saveCurrentRuleSet = async () => {
    if (!newRuleSetName.trim()) {
      setError("请输入规则集名称");
      return;
    }
    
    try {
      const response = await fetch("http://localhost:8002/api/v1/simple-rules/save", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        body: JSON.stringify({
          name: newRuleSetName,
          description: newRuleSetDescription
        }),
      });
      
      if (!response.ok) {
        throw new Error(`保存规则集失败: ${response.status}`);
      }
      
      await loadSavedRuleSets();
      setShowSaveDialog(false);
      setNewRuleSetName("");
      setNewRuleSetDescription("");
    } catch (err) {
      setError(err instanceof Error ? err.message : "保存规则集失败");
    }
  };

  // 切换分类启用状态
  const toggleCategoryEnabled = async (categoryId: string, enabled: boolean) => {
    try {
      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/category/${categoryId}/enabled?enabled=${enabled}`, {
        method: 'PUT',
        mode: 'cors',
      });
      
      if (!response.ok) {
        throw new Error(`切换分类状态失败: ${response.status}`);
      }
      
      await loadCurrentRuleSet();
      await loadStatistics();
    } catch (err) {
      setError(err instanceof Error ? err.message : "切换分类状态失败");
    }
  };

  // 切换主规则启用状态
  const toggleMainRuleEnabled = async (mainRuleId: string, enabled: boolean) => {
    try {
      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/main-rule/${mainRuleId}/enabled?enabled=${enabled}`, {
        method: 'PUT',
        mode: 'cors',
      });
      
      if (!response.ok) {
        throw new Error(`切换主规则状态失败: ${response.status}`);
      }
      
      await loadCurrentRuleSet();
      await loadStatistics();
    } catch (err) {
      setError(err instanceof Error ? err.message : "切换主规则状态失败");
    }
  };

  // 切换子规则启用状态
  const toggleSubRuleEnabled = async (subRuleId: string, enabled: boolean) => {
    try {
      const response = await fetch(`http://localhost:8002/api/v1/simple-rules/sub-rule/${subRuleId}/enabled?enabled=${enabled}`, {
        method: 'PUT',
        mode: 'cors',
      });
      
      if (!response.ok) {
        throw new Error(`切换子规则状态失败: ${response.status}`);
      }
      
      await loadCurrentRuleSet();
      await loadStatistics();
    } catch (err) {
      setError(err instanceof Error ? err.message : "切换子规则状态失败");
    }
  };

  // 切换分类展开状态
  const toggleCategoryExpanded = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // 切换主规则展开状态
  const toggleMainRuleExpanded = (mainRuleId: string) => {
    const newExpanded = new Set(expandedMainRules);
    if (newExpanded.has(mainRuleId)) {
      newExpanded.delete(mainRuleId);
    } else {
      newExpanded.add(mainRuleId);
    }
    setExpandedMainRules(newExpanded);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="text-4xl mb-4">⚙️</div>
          <p className="text-gray-600">加载规则管理器...</p>
        </div>
      </div>
    );
  }

  if (!currentRuleSet) {
    return (
      <div className="text-center py-12">
        <div className="text-4xl mb-4">❌</div>
        <p className="text-gray-600">无法加载规则集</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600 text-sm">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={() => setError(null)}
          >
            关闭
          </Button>
        </div>
      )}

      {/* 顶部控制区域 */}
      <Card>
        <CardHeader>
          <CardTitle>规则集管理</CardTitle>
          <CardDescription>
            当前规则集：{currentRuleSet.name}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 规则集选择 */}
            <div>
              <Label className="text-sm font-medium mb-2 block">切换规则集</Label>
              <Select value={currentRuleSet.id} onValueChange={switchRuleSet}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {savedRuleSets.map((ruleSet) => (
                    <SelectItem key={ruleSet.id} value={ruleSet.id}>
                      {ruleSet.name} {ruleSet.is_default && "(默认)"}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 保存按钮 */}
            <div className="flex items-end">
              <Button 
                onClick={() => setShowSaveDialog(true)}
                className="w-full"
              >
                💾 保存为新规则集
              </Button>
            </div>

            {/* 统计信息 */}
            <div className="text-sm">
              {statistics && (
                <div className="space-y-1">
                  <div>规则大类: {statistics.categories.enabled}/{statistics.categories.total}</div>
                  <div>一级规则: {statistics.main_rules.enabled}/{statistics.main_rules.total}</div>
                  <div>二级规则: {statistics.sub_rules.enabled}/{statistics.sub_rules.total}</div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 保存对话框 */}
      {showSaveDialog && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle>保存规则集</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="ruleset-name">规则集名称</Label>
              <Input
                id="ruleset-name"
                value={newRuleSetName}
                onChange={(e) => setNewRuleSetName(e.target.value)}
                placeholder="输入规则集名称"
              />
            </div>
            <div>
              <Label htmlFor="ruleset-description">规则集描述</Label>
              <Input
                id="ruleset-description"
                value={newRuleSetDescription}
                onChange={(e) => setNewRuleSetDescription(e.target.value)}
                placeholder="输入规则集描述（可选）"
              />
            </div>
            <div className="flex space-x-2">
              <Button onClick={saveCurrentRuleSet}>保存</Button>
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>取消</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 规则层级展示 */}
      <div className="space-y-4">
        {currentRuleSet.categories.map((category) => (
          <CategoryCard
            key={category.id}
            category={category}
            isExpanded={expandedCategories.has(category.id)}
            onToggleExpanded={() => toggleCategoryExpanded(category.id)}
            onToggleEnabled={(enabled) => toggleCategoryEnabled(category.id, enabled)}
            expandedMainRules={expandedMainRules}
            onToggleMainRuleExpanded={toggleMainRuleExpanded}
            onToggleMainRuleEnabled={toggleMainRuleEnabled}
            onToggleSubRuleEnabled={toggleSubRuleEnabled}
            onTestSubRule={setTestingSubRule}
          />
        ))}
      </div>

      {/* 测试模态框 */}
      {testingSubRule && (
        <RuleTestModal
          subRule={testingSubRule}
          onClose={() => setTestingSubRule(null)}
        />
      )}
    </div>
  );
}

// 分类卡片组件
function CategoryCard({ 
  category, 
  isExpanded, 
  onToggleExpanded, 
  onToggleEnabled,
  expandedMainRules,
  onToggleMainRuleExpanded,
  onToggleMainRuleEnabled,
  onToggleSubRuleEnabled,
  onTestSubRule
}: {
  category: RuleCategory;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onToggleEnabled: (enabled: boolean) => void;
  expandedMainRules: Set<string>;
  onToggleMainRuleExpanded: (mainRuleId: string) => void;
  onToggleMainRuleEnabled: (mainRuleId: string, enabled: boolean) => void;
  onToggleSubRuleEnabled: (subRuleId: string, enabled: boolean) => void;
  onTestSubRule: (subRule: SubRule) => void;
}) {
  const enabledMainRules = category.main_rules.filter(rule => rule.enabled).length;
  const totalMainRules = category.main_rules.length;

  return (
    <Card className={`transition-all ${!category.enabled ? 'opacity-60' : ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleExpanded}
              className="p-1"
            >
              {isExpanded ? "📂" : "📁"}
            </Button>
            <div>
              <CardTitle className="flex items-center space-x-2">
                <span>{category.icon}</span>
                <span>{category.name}</span>
                <Badge variant="outline">
                  {enabledMainRules}/{totalMainRules}
                </Badge>
              </CardTitle>
              <CardDescription>{category.description}</CardDescription>
            </div>
          </div>
          <Switch
            checked={category.enabled}
            onCheckedChange={onToggleEnabled}
          />
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent>
          <div className="space-y-3">
            {category.main_rules.map((mainRule) => (
              <MainRuleCard
                key={mainRule.id}
                mainRule={mainRule}
                isExpanded={expandedMainRules.has(mainRule.id)}
                onToggleExpanded={() => onToggleMainRuleExpanded(mainRule.id)}
                onToggleEnabled={(enabled) => onToggleMainRuleEnabled(mainRule.id, enabled)}
                onToggleSubRuleEnabled={onToggleSubRuleEnabled}
                onTestSubRule={onTestSubRule}
              />
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}

// 主规则卡片组件
function MainRuleCard({
  mainRule,
  isExpanded,
  onToggleExpanded,
  onToggleEnabled,
  onToggleSubRuleEnabled,
  onTestSubRule
}: {
  mainRule: MainRule;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onToggleEnabled: (enabled: boolean) => void;
  onToggleSubRuleEnabled: (subRuleId: string, enabled: boolean) => void;
  onTestSubRule: (subRule: SubRule) => void;
}) {
  const enabledSubRules = mainRule.sub_rules.filter(rule => rule.enabled).length;
  const totalSubRules = mainRule.sub_rules.length;

  return (
    <div className={`border rounded-lg p-4 ${!mainRule.enabled ? 'opacity-60' : ''}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleExpanded}
            className="p-1"
          >
            {isExpanded ? "📖" : "📕"}
          </Button>
          <div>
            <div className="font-medium flex items-center space-x-2">
              <span>{mainRule.name}</span>
              <Badge variant="outline" className="text-xs">
                {enabledSubRules}/{totalSubRules}
              </Badge>
            </div>
            <div className="text-sm text-gray-600">{mainRule.description}</div>
          </div>
        </div>
        <Switch
          checked={mainRule.enabled}
          onCheckedChange={onToggleEnabled}
        />
      </div>
      
      {isExpanded && (
        <div className="ml-8 space-y-2">
          {mainRule.sub_rules.map((subRule) => (
            <SubRuleCard
              key={subRule.id}
              subRule={subRule}
              onToggleEnabled={(enabled) => onToggleSubRuleEnabled(subRule.id, enabled)}
              onTest={() => onTestSubRule(subRule)}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// 子规则卡片组件
function SubRuleCard({
  subRule,
  onToggleEnabled,
  onTest
}: {
  subRule: SubRule;
  onToggleEnabled: (enabled: boolean) => void;
  onTest: () => void;
}) {
  const [showExamples, setShowExamples] = useState(false);

  return (
    <div className={`border rounded p-3 ${!subRule.enabled ? 'opacity-60' : ''}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-sm">{subRule.name}</span>
            <Badge 
              variant={subRule.implementation_type === 'llm' ? 'default' : 'secondary'}
              className="text-xs"
            >
              {subRule.implementation_type === 'llm' ? 'LLM' : '确定性'}
            </Badge>
          </div>
          <div className="text-xs text-gray-600 mb-2">{subRule.description}</div>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowExamples(!showExamples)}
              className="text-xs"
            >
              {showExamples ? "隐藏" : "显示"}示例
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onTest}
              className="text-xs"
            >
              🧪 测试
            </Button>
          </div>
        </div>
        <Switch
          checked={subRule.enabled}
          onCheckedChange={onToggleEnabled}
        />
      </div>
      
      {showExamples && subRule.examples.length > 0 && (
        <div className="mt-3 space-y-2">
          {subRule.examples.map((example, index) => (
            <div key={index} className="bg-gray-50 p-2 rounded text-xs">
              <div className="font-medium text-gray-700 mb-1">{example.description}</div>
              <div className="space-y-1">
                <div><span className="text-gray-600">输入:</span> {example.input_text}</div>
                <div><span className="text-gray-600">输出:</span> {example.output_text}</div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
