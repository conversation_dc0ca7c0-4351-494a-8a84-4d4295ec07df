"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface RuleExample {
  input_text: string;
  output_text: string;
  description: string;
}

interface SubRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  implementation_type: string;
  examples: RuleExample[];
  test_cases: string[];
}

interface TestResult {
  original_text: string;
  transformed_text: string;
  rule_applied: boolean;
  execution_time_ms: number;
}

interface RuleTestModalProps {
  subRule: SubRule;
  onClose: () => void;
}

export default function RuleTestModal({ subRule, onClose }: RuleTestModalProps) {
  const [testText, setTestText] = useState("");
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTestCase, setSelectedTestCase] = useState<string>("");

  // 获取测试用例
  useEffect(() => {
    if (subRule.test_cases.length > 0) {
      setTestText(subRule.test_cases[0]);
      setSelectedTestCase(subRule.test_cases[0]);
    } else if (subRule.examples.length > 0) {
      setTestText(subRule.examples[0].input_text);
    }
  }, [subRule]);

  // 执行测试
  const runTest = async () => {
    if (!testText.trim()) {
      setError("请输入测试文本");
      return;
    }

    setLoading(true);
    setError(null);
    setTestResult(null);

    try {
      const response = await fetch("http://localhost:8002/api/v1/simple-rules/sub-rule/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        mode: 'cors',
        body: JSON.stringify({
          sub_rule_id: subRule.id,
          test_text: testText
        }),
      });

      if (!response.ok) {
        throw new Error(`测试失败: ${response.status}`);
      }

      const result = await response.json();
      setTestResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : "测试过程中发生错误");
    } finally {
      setLoading(false);
    }
  };

  // 使用预设测试用例
  const useTestCase = (testCase: string) => {
    setTestText(testCase);
    setSelectedTestCase(testCase);
    setTestResult(null);
  };

  // 使用示例
  const useExample = (example: RuleExample) => {
    setTestText(example.input_text);
    setSelectedTestCase("");
    setTestResult(null);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <span>🧪 测试规则</span>
                  <Badge 
                    variant={subRule.implementation_type === 'llm' ? 'default' : 'secondary'}
                  >
                    {subRule.implementation_type === 'llm' ? 'LLM' : '确定性'}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  {subRule.name} - {subRule.description}
                </CardDescription>
              </div>
              <Button variant="outline" onClick={onClose}>
                ✕ 关闭
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* 错误提示 */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* 测试用例选择 */}
            {(subRule.test_cases.length > 0 || subRule.examples.length > 0) && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">快速选择测试内容</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* 预设测试用例 */}
                    {subRule.test_cases.length > 0 && (
                      <div>
                        <Label className="text-sm font-medium mb-2 block">预设测试用例</Label>
                        <div className="space-y-2">
                          {subRule.test_cases.map((testCase, index) => (
                            <Button
                              key={index}
                              variant={selectedTestCase === testCase ? "default" : "outline"}
                              size="sm"
                              onClick={() => useTestCase(testCase)}
                              className="w-full text-left justify-start h-auto p-2"
                            >
                              <div className="text-xs truncate">
                                {testCase.length > 50 ? testCase.substring(0, 50) + "..." : testCase}
                              </div>
                            </Button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 示例 */}
                    {subRule.examples.length > 0 && (
                      <div>
                        <Label className="text-sm font-medium mb-2 block">转换示例</Label>
                        <div className="space-y-2">
                          {subRule.examples.map((example, index) => (
                            <Button
                              key={index}
                              variant="outline"
                              size="sm"
                              onClick={() => useExample(example)}
                              className="w-full text-left justify-start h-auto p-2"
                            >
                              <div className="text-xs">
                                <div className="font-medium">{example.description}</div>
                                <div className="text-gray-600 truncate">
                                  {example.input_text.length > 30 
                                    ? example.input_text.substring(0, 30) + "..." 
                                    : example.input_text}
                                </div>
                              </div>
                            </Button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 测试输入 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">测试文本</CardTitle>
                  <Button onClick={runTest} disabled={loading}>
                    {loading ? "测试中..." : "🧪 执行测试"}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  placeholder="输入要测试的文本..."
                  className="min-h-[120px] resize-none"
                />
                <div className="text-sm text-gray-500 mt-2">
                  字符数: {testText.length}
                </div>
              </CardContent>
            </Card>

            {/* 测试结果 */}
            {testResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">测试结果</CardTitle>
                </CardHeader>
                <CardContent>
                  {/* 执行统计 */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {testResult.rule_applied ? "✅" : "❌"}
                      </div>
                      <div className="text-sm text-gray-600">规则应用</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {testResult.execution_time_ms.toFixed(2)}ms
                      </div>
                      <div className="text-sm text-gray-600">执行时间</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {testResult.transformed_text.length}
                      </div>
                      <div className="text-sm text-gray-600">结果字符数</div>
                    </div>
                  </div>

                  {/* 对比结果 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600 mb-2 block">
                        原始文本
                      </Label>
                      <div className="p-3 bg-gray-50 border rounded-md min-h-[120px] whitespace-pre-wrap text-sm">
                        {testResult.original_text}
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600 mb-2 block">
                        转换结果
                      </Label>
                      <div className="p-3 bg-blue-50 border rounded-md min-h-[120px] whitespace-pre-wrap text-sm">
                        {testResult.transformed_text}
                      </div>
                    </div>
                  </div>

                  {/* 变化说明 */}
                  {testResult.rule_applied ? (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="text-green-800 text-sm">
                        ✅ 规则已成功应用，文本发生了变化
                      </div>
                    </div>
                  ) : (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <div className="text-yellow-800 text-sm">
                        ⚠️ 规则未应用，可能是条件不匹配或规则已禁用
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* 规则详情 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">规则详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">规则名称:</span>
                      <span className="ml-2">{subRule.name}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">实现方式:</span>
                      <span className="ml-2">
                        {subRule.implementation_type === 'llm' ? 'LLM处理' : '确定性规则'}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">启用状态:</span>
                      <span className="ml-2">{subRule.enabled ? '已启用' : '已禁用'}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">示例数量:</span>
                      <span className="ml-2">{subRule.examples.length} 个</span>
                    </div>
                  </div>
                  
                  <div>
                    <span className="font-medium text-gray-700">规则描述:</span>
                    <p className="text-sm text-gray-600 mt-1">{subRule.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={onClose}>
                关闭
              </Button>
              <Button onClick={runTest} disabled={loading}>
                {loading ? "测试中..." : "重新测试"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
