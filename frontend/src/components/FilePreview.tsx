"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

interface FileInfo {
  file_id: string;
  original_filename: string;
  file_size: number;
  file_type: string;
  character_count: number;
  line_count: number;
  text_preview: string;
  upload_time: string;
}

interface FilePreviewProps {
  fileInfo: FileInfo;
  onUseFile: (content: string) => void;
  onRemoveFile: () => void;
  onEditContent: (content: string) => void;
}

export default function FilePreview({ 
  fileInfo, 
  onUseFile, 
  onRemoveFile, 
  onEditContent 
}: FilePreviewProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState("");
  const [fullContent, setFullContent] = useState<string | null>(null);
  const [isLoadingContent, setIsLoadingContent] = useState(false);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const loadFullContent = async () => {
    if (fullContent) {
      setIsExpanded(!isExpanded);
      return;
    }

    setIsLoadingContent(true);
    try {
      const response = await fetch(`http://localhost:8002/api/v1/file/${fileInfo.file_id}`, {
        mode: 'cors',
      });
      if (response.ok) {
        const data = await response.json();
        setFullContent(data.text_content);
        setEditedContent(data.text_content);
        setIsExpanded(true);
      } else {
        console.error('Failed to load file content');
      }
    } catch (error) {
      console.error('Error loading file content:', error);
    } finally {
      setIsLoadingContent(false);
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    setFullContent(editedContent);
    onEditContent(editedContent);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedContent(fullContent || "");
    setIsEditing(false);
  };

  const handleUseFile = () => {
    const contentToUse = fullContent || fileInfo.text_preview;
    onUseFile(contentToUse);
  };

  return (
    <Card className="border-green-200 bg-green-50">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <span className="text-green-600">
                {fileInfo.file_type === 'txt' ? '📄' : '📝'}
              </span>
              {fileInfo.original_filename}
            </CardTitle>
            <CardDescription>
              上传时间: {formatDate(fileInfo.upload_time)}
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onRemoveFile}
            className="text-red-600 hover:text-red-700"
          >
            移除
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 文件信息 */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <Label className="text-gray-600">文件大小</Label>
            <p className="font-medium">{formatFileSize(fileInfo.file_size)}</p>
          </div>
          <div>
            <Label className="text-gray-600">文件类型</Label>
            <p className="font-medium">{fileInfo.file_type.toUpperCase()}</p>
          </div>
          <div>
            <Label className="text-gray-600">字符数</Label>
            <p className="font-medium">{fileInfo.character_count.toLocaleString()}</p>
          </div>
          <div>
            <Label className="text-gray-600">行数</Label>
            <p className="font-medium">{fileInfo.line_count.toLocaleString()}</p>
          </div>
        </div>

        <Separator />

        {/* 内容预览 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label>内容预览</Label>
            <div className="space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={loadFullContent}
                disabled={isLoadingContent}
              >
                {isLoadingContent ? "加载中..." : isExpanded ? "收起" : "展开全文"}
              </Button>
              {isExpanded && !isEditing && (
                <Button variant="outline" size="sm" onClick={handleEdit}>
                  编辑
                </Button>
              )}
            </div>
          </div>

          {isExpanded ? (
            <div className="space-y-2">
              {isEditing ? (
                <>
                  <Textarea
                    value={editedContent}
                    onChange={(e) => setEditedContent(e.target.value)}
                    className="min-h-[300px] resize-none"
                    placeholder="编辑文件内容..."
                  />
                  <div className="flex space-x-2">
                    <Button size="sm" onClick={handleSaveEdit}>
                      保存
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                      取消
                    </Button>
                  </div>
                </>
              ) : (
                <div className="p-3 bg-white border rounded-md max-h-[300px] overflow-y-auto whitespace-pre-wrap text-sm">
                  {fullContent || "加载中..."}
                </div>
              )}
            </div>
          ) : (
            <div className="p-3 bg-white border rounded-md">
              <p className="text-sm text-gray-700 whitespace-pre-wrap">
                {fileInfo.text_preview}
              </p>
              {fileInfo.character_count > 200 && (
                <p className="text-xs text-gray-500 mt-2">
                  ... 还有 {fileInfo.character_count - 200} 个字符
                </p>
              )}
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-2">
          <Button onClick={handleUseFile} className="flex-1">
            🚀 使用此文件进行转换
          </Button>
          <Button variant="outline" onClick={() => window.open(`http://localhost:8002/api/v1/file/${fileInfo.file_id}`)}>
            📥 下载原文件
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
