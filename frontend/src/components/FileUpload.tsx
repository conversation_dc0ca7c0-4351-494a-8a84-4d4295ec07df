"use client";

import { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useDropzone } from "react-dropzone";

interface FileUploadProps {
  onFileUploaded: (fileInfo: any) => void;
  onError: (error: string) => void;
}

interface FileInfo {
  file_id: string;
  original_filename: string;
  file_size: number;
  file_type: string;
  character_count: number;
  line_count: number;
  text_preview: string;
  upload_time: string;
}

export default function FileUpload({ onFileUploaded, onError }: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    
    // 验证文件类型
    const allowedTypes = ['text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      onError("不支持的文件类型。请上传 .txt 或 .docx 文件");
      return;
    }

    // 验证文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      onError("文件大小超过限制 (10MB)");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await fetch('http://localhost:8002/api/v1/file/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '上传失败');
      }

      const fileInfo: FileInfo = await response.json();
      onFileUploaded(fileInfo);

    } catch (error) {
      onError(error instanceof Error ? error.message : '上传过程中发生错误');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [onFileUploaded, onError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    multiple: false,
    disabled: isUploading
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>📁 文件上传</CardTitle>
        <CardDescription>
          支持 .txt 和 .docx 格式，最大 10MB
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
            ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
            ${isUploading ? 'pointer-events-none opacity-50' : ''}
          `}
        >
          <input {...getInputProps()} />
          
          {isUploading ? (
            <div className="space-y-4">
              <div className="text-2xl">⏳</div>
              <div>
                <p className="text-sm text-gray-600 mb-2">上传中...</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{uploadProgress}%</p>
              </div>
            </div>
          ) : isDragActive ? (
            <div className="space-y-2">
              <div className="text-4xl">📂</div>
              <p className="text-lg font-medium text-blue-600">释放文件以上传</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-4xl">📄</div>
              <div>
                <p className="text-lg font-medium text-gray-700 mb-2">
                  拖拽文件到这里，或点击选择文件
                </p>
                <p className="text-sm text-gray-500">
                  支持 .txt 和 .docx 格式
                </p>
              </div>
              <Button variant="outline" disabled={isUploading}>
                选择文件
              </Button>
            </div>
          )}
        </div>

        <div className="mt-4 text-xs text-gray-500">
          <p>• 支持的格式：.txt (文本文件)、.docx (Word文档)</p>
          <p>• 文件大小限制：10MB</p>
          <p>• 上传后将自动提取文本内容用于转换</p>
        </div>
      </CardContent>
    </Card>
  );
}
